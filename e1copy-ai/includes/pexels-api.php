<?php
if (!defined('ABSPATH')) exit; // Evita acesso direto

/**
 * Busca uma imagem do Pexels com base em uma palavra-chave
 * 
 * @param string $keyword Palavra-chave para buscar imagem
 * @return string|false URL da imagem ou false em caso de erro
 */
function e1copy_ai_get_pexels_image($keyword) {
    // Em uma implementação real, você usaria a API do Pexels
    // Para este exemplo, vamos retornar uma imagem genérica
    
    // Imagens genéricas para diferentes categorias
    $generic_images = [
        'tecnologia' => 'https://images.pexels.com/photos/1714208/pexels-photo-1714208.jpeg',
        'saude' => 'https://images.pexels.com/photos/4386467/pexels-photo-4386467.jpeg',
        'esporte' => 'https://images.pexels.com/photos/46798/the-ball-stadion-football-the-pitch-46798.jpeg',
        'comida' => 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg',
        'viagem' => 'https://images.pexels.com/photos/1271619/pexels-photo-1271619.jpeg',
        'negocio' => 'https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg',
        'educacao' => 'https://images.pexels.com/photos/256520/pexels-photo-256520.jpeg',
        'moda' => 'https://images.pexels.com/photos/934070/pexels-photo-934070.jpeg',
    ];
    
    // Verificar se a palavra-chave corresponde a alguma categoria
    foreach ($generic_images as $category => $image_url) {
        if (stripos($keyword, $category) !== false) {
            return $image_url;
        }
    }
    
    // Se não encontrar correspondência, retorna uma imagem padrão
    return 'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg';
}

/**
 * Adiciona uma imagem em destaque do Pexels a um post
 * 
 * @param int $post_id ID do post
 * @param string $keyword Palavra-chave para buscar imagem
 * @return int|false ID do anexo ou false em caso de erro
 */
function e1copy_ai_add_featured_image_from_pexels($post_id, $keyword) {
    // Buscar URL da imagem
    $image_url = e1copy_ai_get_pexels_image($keyword);
    
    if (!$image_url) {
        return false;
    }
    
    // Baixar e anexar a imagem
    $upload_dir = wp_upload_dir();
    $image_data = file_get_contents($image_url);
    $filename = 'pexels-' . sanitize_title($keyword) . '-' . uniqid() . '.jpg';
    
    if ($image_data) {
        $file = $upload_dir['path'] . '/' . $filename;
        file_put_contents($file, $image_data);
        
        $wp_filetype = wp_check_filetype($filename, null);
        $attachment = array(
            'post_mime_type' => $wp_filetype['type'],
            'post_title' => sanitize_file_name($filename),
            'post_content' => '',
            'post_status' => 'inherit'
        );
        
        $attach_id = wp_insert_attachment($attachment, $file, $post_id);
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attach_data = wp_generate_attachment_metadata($attach_id, $file);
        wp_update_attachment_metadata($attach_id, $attach_data);
        set_post_thumbnail($post_id, $attach_id);
        
        return $attach_id;
    }
    
    return false;
}
