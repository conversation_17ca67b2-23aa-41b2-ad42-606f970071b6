<?php
if (!defined('ABSPATH')) exit; // Bloqueia acesso direto

function e1copy_ai_my_posts_page() {
    // Verificar mensagens
    if (isset($_GET['message'])) {
        $messages = [
            'created' => 'Post criado com sucesso!',
            'updated' => 'Post atualizado com sucesso!',
            'deleted' => 'Post excluído com sucesso!',
            'processed' => 'Post marcado como processado com sucesso!'
        ];

        if (isset($messages[$_GET['message']])) {
            echo '<div class="notice notice-success is-dismissible"><p>' . esc_html($messages[$_GET['message']]) . '</p></div>';
        }
    }

    // Verificar se estamos no modo de formulário
    if (isset($_GET['action']) && $_GET['action'] === 'new') {
        $post_type = isset($_GET['type']) ? $_GET['type'] : 'blog';
        e1copy_ai_display_post_form(0, $post_type);
        return;
    }

    // Verificar se estamos no modo de edição
    if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['post_id'])) {
        $post_id = intval($_GET['post_id']);
        e1copy_ai_display_post_form($post_id);
        return;
    }

    // Processar exclusão de post
    if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['post_id']) && isset($_GET['_wpnonce'])) {
        $post_id = intval($_GET['post_id']);

        // Verificar nonce
        if (!wp_verify_nonce($_GET['_wpnonce'], 'delete_post_' . $post_id)) {
            wp_die('Ação não autorizada.');
        }

        // Excluir o post
        $result = e1copy_ai_delete_my_post($post_id);

        if ($result) {
            wp_redirect(admin_url('admin.php?page=e1copy-ai-my-posts&message=deleted'));
            exit;
        } else {
            wp_die('Erro ao remover o post.');
        }
    }

    // Processar marcação de post como processado
    if (isset($_GET['action']) && $_GET['action'] === 'mark_processed' && isset($_GET['post_id']) && isset($_GET['_wpnonce'])) {
        $post_id = intval($_GET['post_id']);

        // Verificar nonce
        if (!wp_verify_nonce($_GET['_wpnonce'], 'mark_processed_' . $post_id)) {
            wp_die('Ação não autorizada.');
        }

        // Obter o post atual
        $post = e1copy_ai_get_my_post($post_id);
        if (!$post) {
            wp_die('Post não encontrado.');
        }

        // Atualizar o post para marcar como processado
        $post['processed'] = 1;
        $result = e1copy_ai_save_my_post($post);

        // Log para depuração
        error_log('E1Copy My Posts: Marcando post ID ' . $post_id . ' como processado via interface (processed = 1)');

        if ($result) {
            wp_redirect(admin_url('admin.php?page=e1copy-ai-my-posts&message=processed'));
            exit;
        } else {
            wp_die('Erro ao marcar o post como processado.');
        }
    }

    // Processar salvamento de post
    if (isset($_POST['action']) && $_POST['action'] === 'save_post') {
        // Verificar nonce
        if (!isset($_POST['_wpnonce']) || !wp_verify_nonce($_POST['_wpnonce'], 'e1copy_save_post')) {
            wp_die('Ação não autorizada.');
        }

        // Obter o tipo de post
        $post_type = isset($_POST['post_type']) ? sanitize_text_field($_POST['post_type']) : 'blog';

        // Processar tags
        $tags = sanitize_text_field($_POST['post_tags']);

        // Adicionar tags existentes selecionadas
        if (isset($_POST['post_existing_tags']) && is_array($_POST['post_existing_tags'])) {
            $existing_tags = array_map('sanitize_text_field', $_POST['post_existing_tags']);

            // Se já existem tags, adicionar as novas
            if (!empty($tags)) {
                $tags_array = array_map('trim', explode(',', $tags));
                $tags_array = array_merge($tags_array, $existing_tags);
                $tags_array = array_unique($tags_array);
                $tags = implode(', ', $tags_array);
            } else {
                $tags = implode(', ', $existing_tags);
            }
        }

        // Campos comuns para ambos os tipos
        $post_data = [
            'post_type' => $post_type,
            'keyword' => sanitize_text_field($_POST['keyword']),
            'categories' => isset($_POST['post_category']) ? $_POST['post_category'] : [],
            'tags' => $tags,
            'status' => in_array($_POST['post_status'], ['draft', 'publish']) ? $_POST['post_status'] : 'draft',
            'youtube_video' => esc_url_raw($_POST['youtube_video']),
            'cta_text' => sanitize_text_field($_POST['cta_text']),
            'cta_link' => esc_url_raw($_POST['cta_link']),
        ];

        // Adicionar campos específicos com base no tipo de post
        if ($post_type === 'blog') {
            $post_data['title'] = sanitize_text_field($_POST['post_title']);
            $post_data['image_source'] = sanitize_text_field($_POST['image_source']);
            $post_data['subtitles_count'] = intval($_POST['subtitles_count']);
            $post_data['has_summary'] = isset($_POST['has_summary']) ? 'yes' : 'no';
            $post_data['has_conclusion'] = isset($_POST['has_conclusion']) ? 'yes' : 'no';
            $post_data['has_faq'] = isset($_POST['has_faq']) ? 'yes' : 'no';
            $post_data['has_internal_link'] = isset($_POST['has_internal_link']) ? 'yes' : 'no';
        } elseif ($post_type === 'product') {
            $post_data['title'] = sanitize_text_field($_POST['product_name']); // Usar o nome do produto como título
            $post_data['product_name'] = sanitize_text_field($_POST['product_name']);
            $post_data['product_description'] = wp_kses_post($_POST['product_description']);
            $post_data['post_cover_image'] = esc_url_raw($_POST['post_cover_image']);
            $post_data['post_cover_image_id'] = isset($_POST['post_cover_image_id']) ? intval($_POST['post_cover_image_id']) : 0;
            $post_data['affiliate_id'] = isset($_POST['affiliate_id']) ? intval($_POST['affiliate_id']) : 0;

            // Processar imagens do produto
            if (isset($_POST['product_images']) && !empty($_POST['product_images'])) {
                // Verificar se é um JSON válido
                $product_images = json_decode(stripslashes($_POST['product_images']), true);
                if (is_array($product_images)) {
                    // Verificar se há pelo menos 5 imagens
                    if (count($product_images) < 5) {
                        wp_die('É necessário adicionar no mínimo 5 imagens do produto. Por favor, volte e adicione mais imagens.');
                    }

                    // Sanitizar cada URL de imagem
                    foreach ($product_images as &$image_url) {
                        $image_url = esc_url_raw($image_url);
                    }
                    $post_data['product_images'] = json_encode($product_images);
                } else {
                    // Se não for um JSON válido, exibir erro
                    wp_die('Formato inválido para as imagens do produto. Por favor, volte e tente novamente.');
                }
            } else {
                // Se não houver imagens, exibir erro
                wp_die('É necessário adicionar no mínimo 5 imagens do produto. Por favor, volte e adicione imagens.');
            }
        }

        // Não adicionamos mais o campo 'processed' no formulário
        // O processamento será feito apenas via API

        // Verificar se é uma atualização ou novo post
        if (isset($_POST['post_id']) && intval($_POST['post_id']) > 0) {
            $post_data['id'] = intval($_POST['post_id']);
        }

        // Salvar os dados
        $post_id = e1copy_ai_save_my_post($post_data);

        if ($post_id) {
            $message = isset($_POST['post_id']) ? 'updated' : 'created';
            wp_redirect(admin_url("admin.php?page=e1copy-ai-my-posts&message={$message}"));
            exit;
        } else {
            global $wpdb;
            $error_message = 'Erro ao salvar o post.';

            if (!empty($wpdb->last_error)) {
                $error_message .= '<br><br>Erro no banco de dados do WordPress: [' . $wpdb->last_error . ']';
                $error_message .= '<br><br>Última consulta SQL: ' . $wpdb->last_query;
            }

            // Verificar se a tabela tem todas as colunas necessárias
            $table_name = $wpdb->prefix . 'e1copy_my_posts';
            $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
            $column_names = [];
            foreach ($columns as $column) {
                $column_names[] = $column->Field;
            }

            $error_message .= '<br><br>Colunas existentes na tabela: ' . implode(', ', $column_names);

            // Forçar a atualização da tabela
            e1copy_ai_create_my_posts_table();

            $error_message .= '<br><br>A estrutura da tabela foi atualizada. Por favor, tente novamente.';

            wp_die($error_message);
        }
    }

    // Exibir a listagem de posts
    ?>
    <style>
        .actions-column .button {
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .button-link-delete {
            color: #a00;
            border-color: #a00;
        }
        .button-link-delete:hover {
            color: #dc3232;
            border-color: #dc3232;
            background: #f8f8f8;
        }
    </style>
    <div class="wrap">
        <h1 class="wp-heading-inline">Meus Posts</h1>
        <a href="#" class="page-title-action" id="new-post-button">+ Novo Post</a>
        <p class="description">Esta página lista todos os posts cadastrados através do formulário "Meus Posts". Clique em "+ Novo Post" para adicionar um novo registro.</p>

        <!-- Modal de seleção de tipo de post -->
        <div id="post-type-modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
            <div style="background-color: #fefefe; margin: 15% auto; padding: 20px; border: 1px solid #888; width: 50%; max-width: 500px; border-radius: 5px; box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);">
                <h2 style="margin-top: 0;">Selecione o tipo de post</h2>
                <p>Escolha o tipo de conteúdo que deseja criar:</p>
                <div style="display: flex; justify-content: space-between; margin-top: 20px;">
                    <a href="<?php echo admin_url('admin.php?page=e1copy-ai-my-posts&action=new&type=blog'); ?>" class="button button-primary" style="width: 48%; text-align: center; padding: 15px 0; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                        <span class="dashicons dashicons-welcome-write-blog" style="font-size: 24px; width: 24px; height: 24px; margin-bottom: 8px;"></span>
                        Blog Tradicional
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=e1copy-ai-my-posts&action=new&type=product'); ?>" class="button button-primary" style="width: 48%; text-align: center; padding: 15px 0; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                        <span class="dashicons dashicons-cart" style="font-size: 24px; width: 24px; height: 24px; margin-bottom: 8px;"></span>
                        Produto Review
                    </a>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button id="close-modal" class="button">Cancelar</button>
                </div>
            </div>
        </div>

        <script>
            jQuery(document).ready(function($) {
                // Abrir modal ao clicar no botão "Novo Post"
                $('#new-post-button').click(function(e) {
                    e.preventDefault();
                    $('#post-type-modal').show();
                });

                // Fechar modal ao clicar no botão "Cancelar"
                $('#close-modal').click(function() {
                    $('#post-type-modal').hide();
                });

                // Fechar modal ao clicar fora dele
                $(window).click(function(e) {
                    if (e.target.id === 'post-type-modal') {
                        $('#post-type-modal').hide();
                    }
                });
            });
        </script>

        <?php
        // Buscar posts
        $posts = e1copy_ai_get_all_my_posts();

        if (empty($posts)): ?>
            <div class="notice notice-info">
                <p>Nenhum post encontrado. <a href="#" id="empty-new-post-button">Criar novo post</a></p>
            </div>

            <script>
                jQuery(document).ready(function($) {
                    $('#empty-new-post-button').click(function(e) {
                        e.preventDefault();
                        $('#post-type-modal').show();
                    });
                });
            </script>
        <?php else: ?>
            <table class="wp-list-table widefat fixed striped posts">
                <thead>
                    <tr>
                        <th scope="col" class="manage-column column-title column-primary">Título</th>
                        <th scope="col" class="manage-column">Tipo</th>
                        <th scope="col" class="manage-column">Palavra-chave</th>
                        <th scope="col" class="manage-column">Status</th>
                        <th scope="col" class="manage-column">Processado</th>
                        <th scope="col" class="manage-column">Data de Criação</th>
                        <th scope="col" class="manage-column">Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($posts as $post): ?>
                    <tr>
                        <td class="title column-title has-row-actions column-primary">
                            <strong><a href="<?php echo admin_url('admin.php?page=e1copy-ai-my-posts&action=edit&post_id=' . $post['id']); ?>"><?php echo esc_html($post['title']); ?></a></strong>
                            <div class="row-actions">
                                <span class="edit"><a href="<?php echo admin_url('admin.php?page=e1copy-ai-my-posts&action=edit&post_id=' . $post['id']); ?>">Editar</a> | </span>
                                <span class="trash"><a href="<?php echo wp_nonce_url(admin_url('admin.php?page=e1copy-ai-my-posts&action=delete&post_id=' . $post['id']), 'delete_post_' . $post['id']); ?>" class="submitdelete" onclick="return confirm('Tem certeza que deseja excluir este post?')">Excluir</a></span>
                            </div>
                        </td>
                        <td>
                            <?php
                            $post_type = isset($post['post_type']) ? $post['post_type'] : 'blog';
                            if ($post_type === 'product') {
                                echo '<span style="color: #0073aa;">Produto Review</span>';
                            } else {
                                echo 'Blog Tradicional';
                            }
                            ?>
                        </td>
                        <td><?php echo esc_html($post['keyword']); ?></td>
                        <td>
                            <?php if ($post['status'] === 'publish'): ?>
                                <span class="post-state">Publicado</span>
                            <?php else: ?>
                                <span class="post-state">Rascunho</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            // Log para depuração
                            error_log('E1Copy My Posts: Post ID ' . $post['id'] . ' tem processed = ' . (isset($post['processed']) ? $post['processed'] : 'não definido'));

                            // Valor 1 no banco significa processado
                            if (isset($post['processed']) && $post['processed'] == 1): ?>
                                <span class="dashicons dashicons-yes" style="color: green;" title="Processado"></span>
                            <?php else: ?>
                                <span class="dashicons dashicons-no" style="color: red;" title="Não processado"></span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo date('d/m/Y H:i', strtotime($post['created_at'])); ?></td>
                        <td class="actions-column">
                            <a href="<?php echo admin_url('admin.php?page=e1copy-ai-my-posts&action=edit&post_id=' . $post['id']); ?>" class="button button-small">Editar</a>
                            <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=e1copy-ai-my-posts&action=delete&post_id=' . $post['id']), 'delete_post_' . $post['id']); ?>" class="button button-small button-link-delete" onclick="return confirm('Tem certeza que deseja remover este post?')">Remover</a>
                            <?php if (!isset($post['processed']) || $post['processed'] == 0): ?>
                            <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=e1copy-ai-my-posts&action=mark_processed&post_id=' . $post['id']), 'mark_processed_' . $post['id']); ?>" class="button button-small button-primary">Marcar como Processado</a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
    <?php
}

/**
 * Exibe o formulário para criar/editar post
 */
function e1copy_ai_display_post_form($post_id = 0, $post_type = 'blog') {
    $post_data = [
        'post_type' => $post_type,
        'title' => '',
        'keyword' => '',
        'categories' => [],
        'tags' => '',
        'status' => ($post_type === 'product') ? 'publish' : 'draft', // Status padrão "publish" para produtos
        'image_source' => 'pexels',
        'subtitles_count' => 5,
        'has_summary' => 'no',
        'has_conclusion' => 'no',
        'has_faq' => 'no',
        'has_internal_link' => 'no',
        'youtube_video' => '',
        'product_name' => '',
        'product_description' => '',
        'product_link' => '',
        'product_images' => json_encode([]),
        'post_cover_image' => '',
        'post_cover_image_id' => 0,
        'affiliate_id' => 0,
    ];

    // Se for edição, carregar dados existentes
    if ($post_id > 0) {
        $saved_post = e1copy_ai_get_my_post($post_id);
        if (!$saved_post) {
            wp_die('Post não encontrado.');
        }

        $post_data = array_merge($post_data, $saved_post);
        $post_type = $post_data['post_type'] ?? 'blog';

        // Log para depuração
        error_log('E1Copy My Posts: Carregando post ID ' . $post_id . ' com processed = ' . (isset($post_data['processed']) ? $post_data['processed'] : 'não definido') . ' e tipo = ' . $post_type);
    }

    $form_title = $post_id ? 'Editar Post' : 'Novo Post';
    $form_subtitle = $post_type === 'blog' ? 'Blog Tradicional' : 'Produto Review';

    ?>
    <div class="wrap">
        <h1><?php echo $form_title; ?> - <?php echo $form_subtitle; ?></h1>
        <p class="description">Este formulário permite registrar informações para um post sem gerar conteúdo automaticamente. Preencha os campos abaixo para cadastrar um novo post.</p>

        <style>
            .required {
                color: #dc3232;
                font-weight: bold;
            }
            .form-table th {
                padding-top: 15px;
            }
            #product_images_container {
                margin-bottom: 15px;
            }
            .validation-message {
                color: #dc3232;
                font-weight: bold;
                margin-top: 5px;
                display: none;
            }
        </style>

        <form method="post" action="<?php echo admin_url('admin.php?page=e1copy-ai-my-posts'); ?>">
            <?php wp_nonce_field('e1copy_save_post'); ?>
            <input type="hidden" name="action" value="save_post">
            <input type="hidden" name="post_type" value="<?php echo esc_attr($post_type); ?>">
            <?php if ($post_type === 'product'): ?>
                <input type="hidden" name="post_status" value="publish">
            <?php endif; ?>
            <?php if ($post_id): ?>
                <input type="hidden" name="post_id" value="<?php echo $post_id; ?>">
            <?php endif; ?>

            <table class="form-table">
                <?php if ($post_type === 'product'): ?>
                <!-- Campos específicos para Produto Review -->
                <tr>
                    <th scope="row"><label for="product_name">Nome do Produto</label> <span class="required">*</span></th>
                    <td>
                        <input type="text" name="product_name" id="product_name" value="<?php echo esc_attr($post_data['product_name']); ?>" class="regular-text" required>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="product_description">Descrição do Produto</label></th>
                    <td>
                        <textarea name="product_description" id="product_description" rows="5" class="large-text" placeholder="Cole aqui informações detalhadas sobre o produto para ajudar a I.A. a criar um conteúdo mais preciso..."><?php echo esc_textarea($post_data['product_description']); ?></textarea>
                        <p class="description">Informações detalhadas sobre o produto que ajudarão a I.A. a criar um conteúdo mais preciso e relevante</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="post_cover_image">Capa do Post</label></th>
                    <td>
                        <input type="text" name="post_cover_image" id="post_cover_image" value="<?php echo esc_url($post_data['post_cover_image']); ?>" class="regular-text">
                        <input type="hidden" name="post_cover_image_id" id="post_cover_image_id" value="<?php echo esc_attr($post_data['post_cover_image_id']); ?>">
                        <button type="button" class="button" id="upload_cover_image_button">Selecionar Imagem</button>
                        <p class="description">Imagem principal que será exibida como capa do post</p>
                        <div id="cover_image_preview" style="margin-top: 10px;">
                            <?php if (!empty($post_data['post_cover_image'])): ?>
                                <img src="<?php echo esc_url($post_data['post_cover_image']); ?>" style="max-width: 300px; height: auto;">
                            <?php endif; ?>
                        </div>
                        <script>
                            jQuery(document).ready(function($) {
                                $('#upload_cover_image_button').click(function(e) {
                                    e.preventDefault();
                                    var image = wp.media({
                                        title: 'Selecionar Imagem de Capa',
                                        multiple: false,
                                        library: {type: 'image'}
                                    }).open().on('select', function() {
                                        var uploaded_image = image.state().get('selection').first();
                                        var image_data = uploaded_image.toJSON();
                                        var image_url = image_data.url;
                                        var image_id = image_data.id;

                                        $('#post_cover_image').val(image_url);
                                        $('#post_cover_image_id').val(image_id);
                                        $('#cover_image_preview').html('<img src="' + image_url + '" style="max-width: 300px; height: auto;">');
                                        console.log('Imagem selecionada - ID: ' + image_id + ', URL: ' + image_url);
                                    });
                                });
                            });
                        </script>
                    </td>
                </tr>

                <?php else: ?>
                <!-- Campos específicos para Blog Tradicional -->
                <tr>
                    <th scope="row"><label for="post_title">Título</label></th>
                    <td>
                        <input type="text" name="post_title" id="post_title" value="<?php echo esc_attr($post_data['title']); ?>" class="regular-text" required>
                    </td>
                </tr>
                <?php endif; ?>

                <!-- Campos comuns a ambos os tipos -->
                <tr>
                    <th scope="row"><label for="keyword">Palavra-Chave</label></th>
                    <td>
                        <input type="text" name="keyword" id="keyword" value="<?php echo esc_attr($post_data['keyword']); ?>" class="regular-text">
                        <p class="description">Palavra-chave principal para otimização SEO</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="post_category">Categoria</label></th>
                    <td>
                        <?php
                        $categories = get_categories(['hide_empty' => false]);
                        if (!empty($categories)) {
                            echo '<select name="post_category[]" id="post_category" multiple="multiple" style="width: 100%; max-width: 400px;">';
                            foreach ($categories as $category) {
                                $selected = in_array($category->term_id, (array)$post_data['categories']) ? 'selected="selected"' : '';
                                echo '<option value="' . esc_attr($category->term_id) . '" ' . $selected . '>' . esc_html($category->name) . '</option>';
                            }
                            echo '</select>';
                        } else {
                            echo '<p>Nenhuma categoria encontrada. <a href="' . admin_url('edit-tags.php?taxonomy=category') . '">Criar categorias</a></p>';
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="post_tags">Tags</label></th>
                    <td>
                        <input type="text" name="post_tags" id="post_tags" value="<?php echo esc_attr($post_data['tags']); ?>" class="regular-text">
                        <p class="description">Separe as tags com vírgulas</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="post_existing_tags">Tags Existentes</label></th>
                    <td>
                        <?php
                        $tags = get_tags(['hide_empty' => false]);
                        if (!empty($tags)) {
                            echo '<select name="post_existing_tags[]" id="post_existing_tags" multiple="multiple" style="width: 100%; max-width: 400px;">';
                            foreach ($tags as $tag) {
                                echo '<option value="' . esc_attr($tag->name) . '">' . esc_html($tag->name) . '</option>';
                            }
                            echo '</select>';
                        } else {
                            echo '<p>Nenhuma tag encontrada. <a href="' . admin_url('edit-tags.php?taxonomy=post_tag') . '">Criar tags</a></p>';
                        }
                        ?>
                        <p class="description">Selecione tags existentes para adicionar ao post</p>
                    </td>
                </tr>


                <?php if ($post_type === 'blog'): ?>
                <!-- Campos específicos para Blog Tradicional -->
                <tr>
                    <th scope="row"><label for="image_source">Banco de Imagens</label></th>
                    <td>
                        <select name="image_source" id="image_source">
                            <option value="pexels" <?php selected($post_data['image_source'], 'pexels'); ?>>Pexels</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="subtitles_count">Quantidade de Subtítulos</label></th>
                    <td>
                        <select name="subtitles_count" id="subtitles_count">
                            <option value="5" <?php selected($post_data['subtitles_count'], 5); ?>>5</option>
                            <option value="8" <?php selected($post_data['subtitles_count'], 8); ?>>8</option>
                            <option value="10" <?php selected($post_data['subtitles_count'], 10); ?>>10</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Artigo</th>
                    <td>
                        <fieldset>
                            <legend class="screen-reader-text">Opções do Artigo</legend>
                            <label for="has_summary">
                                <input type="checkbox" name="has_summary" id="has_summary" value="yes" <?php checked($post_data['has_summary'], 'yes'); ?>>
                                Resumo
                            </label><br>
                            <label for="has_conclusion">
                                <input type="checkbox" name="has_conclusion" id="has_conclusion" value="yes" <?php checked($post_data['has_conclusion'], 'yes'); ?>>
                                Conclusão
                            </label><br>
                            <label for="has_faq">
                                <input type="checkbox" name="has_faq" id="has_faq" value="yes" <?php checked($post_data['has_faq'], 'yes'); ?>>
                                FAQ
                            </label><br>
                            <label for="has_internal_link">
                                <input type="checkbox" name="has_internal_link" id="has_internal_link" value="yes" <?php checked($post_data['has_internal_link'], 'yes'); ?>>
                                Link Interno
                            </label>
                        </fieldset>
                    </td>
                </tr>
                <?php endif; ?>

                <!-- Campos comuns a ambos os tipos -->
                <tr>
                    <th scope="row"><label for="youtube_video">Vídeo YouTube</label></th>
                    <td>
                        <input type="url" name="youtube_video" id="youtube_video" value="<?php echo esc_url($post_data['youtube_video']); ?>" class="regular-text">
                        <p class="description">URL do vídeo do YouTube (ex: https://www.youtube.com/watch?v=XXXX)</p>
                    </td>
                </tr>

                <?php if ($post_type === 'product'): ?>
                <!-- Campos específicos para Produto Review -->
                <tr>
                    <th scope="row"><label for="product_images">Imagens do Produto</label> <span class="required">*</span></th>
                    <td>
                        <div id="product_images_container">
                            <input type="hidden" name="product_images" id="product_images" value="<?php echo esc_attr($post_data['product_images']); ?>" required>
                            <button type="button" class="button" id="upload_product_images_button">Adicionar Imagens</button>
                            <p class="description"><strong>Obrigatório:</strong> Adicione no mínimo 5 imagens do produto para exibição na review</p>

                            <div id="images_count_info" style="margin-top: 10px; margin-bottom: 10px; font-weight: bold; color: <?php
                                $product_images = !empty($post_data['product_images']) ? json_decode($post_data['product_images'], true) : [];
                                $images_count = is_array($product_images) ? count($product_images) : 0;
                                echo ($images_count >= 5) ? '#46b450' : '#dc3232';
                            ?>;">
                                Imagens selecionadas: <span id="images_count"><?php echo $images_count; ?></span> (mínimo: 5)
                            </div>

                            <div id="product_images_preview" style="margin-top: 15px; display: flex; flex-wrap: wrap; gap: 10px;">
                                <?php
                                if (!empty($product_images) && is_array($product_images)):
                                    foreach ($product_images as $index => $image_url):
                                ?>
                                    <div class="product-image-item" data-url="<?php echo esc_url($image_url); ?>" style="position: relative; width: 150px; margin-bottom: 10px;">
                                        <img src="<?php echo esc_url($image_url); ?>" style="width: 100%; height: auto; border: 1px solid #ddd; padding: 3px;">
                                        <button type="button" class="remove-product-image button button-small" style="position: absolute; top: 5px; right: 5px; background: #f44336; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; padding: 0; line-height: 1; cursor: pointer;" title="Remover imagem">&times;</button>
                                    </div>
                                <?php
                                    endforeach;
                                endif;
                                ?>
                            </div>
                        </div>

                        <script>
                            jQuery(document).ready(function($) {
                                // Função para atualizar o campo hidden com os valores das imagens
                                function updateProductImagesField() {
                                    var images = [];
                                    $('#product_images_preview .product-image-item').each(function() {
                                        images.push($(this).data('url'));
                                    });
                                    $('#product_images').val(JSON.stringify(images));

                                    // Atualizar contador de imagens
                                    var imagesCount = images.length;
                                    $('#images_count').text(imagesCount);

                                    // Atualizar cor do contador baseado no número de imagens
                                    if (imagesCount >= 5) {
                                        $('#images_count_info').css('color', '#46b450'); // Verde
                                    } else {
                                        $('#images_count_info').css('color', '#dc3232'); // Vermelho
                                    }
                                }

                                // Inicializar o campo hidden se estiver vazio
                                if ($('#product_images').val() === '') {
                                    $('#product_images').val(JSON.stringify([]));
                                }

                                // Botão para adicionar imagens
                                $('#upload_product_images_button').click(function(e) {
                                    e.preventDefault();
                                    var images = wp.media({
                                        title: 'Selecionar Imagens do Produto',
                                        multiple: true,
                                        library: {type: 'image'}
                                    }).open().on('select', function() {
                                        var selection = images.state().get('selection');

                                        selection.each(function(attachment) {
                                            var image_url = attachment.toJSON().url;
                                            var imageHtml = '<div class="product-image-item" data-url="' + image_url + '" style="position: relative; width: 150px; margin-bottom: 10px;">' +
                                                '<img src="' + image_url + '" style="width: 100%; height: auto; border: 1px solid #ddd; padding: 3px;">' +
                                                '<button type="button" class="remove-product-image button button-small" style="position: absolute; top: 5px; right: 5px; background: #f44336; color: white; border: none; border-radius: 50%; width: 20px; height: 20px; padding: 0; line-height: 1; cursor: pointer;" title="Remover imagem">&times;</button>' +
                                                '</div>';

                                            $('#product_images_preview').append(imageHtml);
                                        });

                                        updateProductImagesField();
                                    });
                                });

                                // Remover imagem ao clicar no botão de remover
                                $(document).on('click', '.remove-product-image', function() {
                                    $(this).parent('.product-image-item').remove();
                                    updateProductImagesField();
                                });

                                // Validação do formulário
                                $('form').on('submit', function(e) {
                                    var productType = $('input[name="post_type"]').val();
                                    if (productType === 'product') {
                                        var imagesJson = $('#product_images').val();
                                        var images = JSON.parse(imagesJson);

                                        if (images.length < 5) {
                                            e.preventDefault();
                                            alert('É necessário adicionar no mínimo 5 imagens do produto.');
                                            $('#upload_product_images_button').focus();
                                            $('html, body').animate({
                                                scrollTop: $('#product_images_container').offset().top - 100
                                            }, 500);
                                            return false;
                                        }
                                    }
                                });
                            });
                        </script>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="affiliate_id">Afiliado</label></th>
                    <td>
                        <select name="affiliate_id" id="affiliate_id" class="regular-text">
                            <option value="0">Selecione um afiliado</option>
                            <?php
                            $affiliates = e1copy_ai_get_all_affiliates();
                            if (!empty($affiliates)) {
                                foreach ($affiliates as $affiliate) {
                                    $selected = ($post_data['affiliate_id'] == $affiliate['id']) ? 'selected="selected"' : '';
                                    echo '<option value="' . esc_attr($affiliate['id']) . '" ' . $selected . '>' . esc_html($affiliate['title']) . '</option>';
                                }
                            }
                            ?>
                        </select>
                        <p class="description">Selecione um afiliado para este produto ou <a href="<?php echo admin_url('admin.php?page=e1copy-ai-affiliates&action=new'); ?>" target="_blank">crie um novo</a></p>
                    </td>
                </tr>
                <?php endif; ?>
            </table>

            <?php if ($post_type === 'product'): ?>
            <!-- Carregar a biblioteca de mídia do WordPress -->
            <?php wp_enqueue_media(); ?>
            <?php endif; ?>

            <p class="submit">
                <button type="submit" class="button button-primary">Salvar Post</button>
                <a href="<?php echo admin_url('admin.php?page=e1copy-ai-my-posts'); ?>" class="button">Cancelar</a>
            </p>
        </form>
    </div>

    <?php
    // Carregar Select2 apenas na página de formulário do plugin
    if (function_exists('e1copy_is_plugin_page') && e1copy_is_plugin_page()) {
        // Verificar se o Select2 já está registrado
        if (!wp_script_is('select2', 'registered')) {
            wp_register_script('select2', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', array('jquery'), '4.1.0', true);
            wp_register_style('select2', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
        }

        // Enfileirar Select2 apenas se necessário
        if (!wp_script_is('select2', 'enqueued')) {
            wp_enqueue_script('select2');
            wp_enqueue_style('select2');
        }

        // Adicionar script inline para inicializar Select2
        ?>
        <script>
        jQuery(document).ready(function($) {
            // Inicializar select2 para categorias múltiplas
            $('#post_category').select2({
                placeholder: 'Selecione as categorias'
            });

            // Inicializar select2 para tags existentes
            $('#post_existing_tags').select2({
                placeholder: 'Selecione as tags existentes',
                tags: true,
                tokenSeparators: [',']
            });

            // Quando selecionar tags existentes, adicionar ao campo de tags
            $('#post_existing_tags').on('change', function() {
                var selectedTags = $(this).val();
                if (selectedTags) {
                    var currentTags = $('#post_tags').val();
                    var tagsArray = currentTags ? currentTags.split(',').map(function(tag) {
                        return tag.trim();
                    }).filter(function(tag) {
                        return tag !== '';
                    }) : [];

                    // Adicionar novas tags selecionadas
                    selectedTags.forEach(function(tag) {
                        if (tagsArray.indexOf(tag) === -1) {
                            tagsArray.push(tag);
                        }
                    });

                    // Atualizar o campo de tags
                    $('#post_tags').val(tagsArray.join(', '));
                }
            });
        });
        </script>
    <?php } ?>
    <?php
}

// Função de formatação de conteúdo removida, pois não estamos mais gerando conteúdo com IA

/**
 * Extrai o ID do vídeo do YouTube a partir da URL
 */
function e1copy_ai_get_youtube_video_id($url) {
    $pattern = '/(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/';
    preg_match($pattern, $url, $matches);
    return isset($matches[1]) ? $matches[1] : false;
}

// A função e1copy_ai_add_featured_image_from_pexels foi movida para includes/pexels-api.php
