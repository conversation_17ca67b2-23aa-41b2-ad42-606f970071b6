<?php
function e1copy_ai_settings_page() {
    // Garantir que o Dashicons esteja disponível
    wp_enqueue_style('dashicons');
    // Verifica se o formulário foi submetido
    if (isset($_POST['submit'])) {
        // Verifica o nonce para segurança
        check_admin_referer('e1copy_ai_settings_update');

        $activation_key = sanitize_text_field($_POST['e1copy_ai_activation_key']);

        // Validar chave de ativação se fornecida
        if (!empty($activation_key)) {
            $validation_result = e1copy_ai_validate_activation_key($activation_key);
            if ($validation_result['valid']) {
                // Registrar site automaticamente se a chave for válida
                e1copy_ai_register_site($activation_key);
                add_settings_error(
                    'e1copy_ai_messages',
                    'e1copy_ai_message',
                    __('Chave de ativação válida! Site registrado com sucesso.', 'e1copy-ai'),
                    'success'
                );
            } else {
                add_settings_error(
                    'e1copy_ai_messages',
                    'e1copy_ai_message',
                    __('Chave de ativação inválida: ' . $validation_result['message'], 'e1copy-ai'),
                    'error'
                );
            }
        }

        // Salva todas as opções
        update_option('e1copy_ai_activation_key', $activation_key);
        update_option('e1copy_ai_rest_api_key', sanitize_text_field($_POST['e1copy_ai_rest_api_key']));

        // Mensagem de sucesso se não houve erro na validação
        if (empty($activation_key) || $validation_result['valid']) {
            add_settings_error(
                'e1copy_ai_messages',
                'e1copy_ai_message',
                __('Configurações salvas com sucesso!', 'e1copy-ai'),
                'success'
            );
        }
    }

    // Obtém os valores atuais
    $activation_key = get_option('e1copy_ai_activation_key');

    // Mostra erros/notificações
    settings_errors('e1copy_ai_messages');
?>
<div class="wrap">
    <h1><?php _e('Configurações do E1Copy AI', 'e1copy-ai'); ?></h1>

    <form method="post">
        <?php wp_nonce_field('e1copy_ai_settings_update'); ?>

        <h2 class="title"><?php _e('Configurações de Ativação', 'e1copy-ai'); ?></h2>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="e1copy_ai_activation_key"><?php _e('Chave de Ativação', 'e1copy-ai'); ?></label>
                </th>
                <td>
                    <input type="password" name="e1copy_ai_activation_key" id="e1copy_ai_activation_key"
                           value="<?php echo esc_attr($activation_key); ?>" class="regular-text">
                    <div class="activation-key-controls" style="margin: 10px 0;">
                        <button type="button" id="toggle-activation-key" class="button button-secondary">
                            <span class="dashicons dashicons-visibility"></span> Mostrar/Ocultar
                        </button>
                        <button type="button" id="validate-activation-key" class="button button-primary" style="margin-left: 10px;">
                            <span class="dashicons dashicons-yes"></span> Validar Chave
                        </button>
                        <span id="activation-status" style="margin-left: 10px;"></span>
                    </div>
                    <p class="description">
                        <?php _e('Chave de ativação fornecida pelo Dashboard E1Copy AI. Esta chave é necessária para liberar todas as funcionalidades do plugin.', 'e1copy-ai'); ?>
                        <br><strong><?php _e('Como obter sua chave:', 'e1copy-ai'); ?></strong>
                    </p>
                    <div class="notice notice-info inline" style="margin: 10px 0; padding: 10px;">
                        <p><strong>📋 Passo a passo:</strong></p>
                        <ol style="margin-left: 20px;">
                            <li>Acesse <a href="https://app.melhorcupom.shop/login" target="_blank"><strong>app.melhorcupom.shop</strong></a></li>
                            <li>Faça login ou crie sua conta</li>
                            <li>Vá em <strong>"Minha Conta"</strong></li>
                            <li>Clique em <strong>"Gerar Nova Chave"</strong></li>
                            <li>Copie a chave e cole aqui</li>
                            <li>Clique em <strong>"Validar Chave"</strong></li>
                        </ol>
                        <p><strong>💡 Dica:</strong> A chave deve começar com <code>e1copy_</code> seguido de 32 caracteres.</p>
                    </div>
                </td>
            </tr>
        </table>

        <h2 class="title"><?php _e('Configurações da API REST', 'e1copy-ai'); ?></h2>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="e1copy_ai_rest_api_key"><?php _e('Chave de API para REST', 'e1copy-ai'); ?></label>
                </th>
                <td>
                    <div class="api-key-container" style="display: flex; align-items: center; margin-bottom: 10px;">
                        <input type="password" name="e1copy_ai_rest_api_key" id="e1copy_ai_rest_api_key"
                               value="<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>" class="regular-text">
                        <div id="api-key-actions" style="display: inline-block; margin-left: 10px;">
                            <!-- Botões serão adicionados via JavaScript -->
                        </div>
                    </div>
                    <div class="api-key-controls" style="margin-bottom: 10px;">
                        <button type="button" id="generate-api-key" class="button button-secondary">Gerar Nova Chave</button>
                        <span id="api-key-message" style="margin-left: 10px; display: none;" class="api-key-message"></span>
                    </div>
                    <p class="description">
                        <?php _e('Esta chave é usada para autenticar solicitações à API REST do plugin. Use os botões para gerar uma nova chave, copiá-la ou alternar sua visibilidade.', 'e1copy-ai'); ?>
                    </p>
                </td>
            </tr>
            <tr>
                <th scope="row">Documentação da API</th>
                <td>
                    <a href="#" id="open-api-docs" class="button button-primary">Ver Documentação da API</a>
                    <p class="description">Clique no botão acima para ver a documentação completa da API REST.</p>
                </td>
            </tr>
        </table>

        <script>
        jQuery(document).ready(function($) {
            // Adicionar botões ao container
            $('#api-key-actions').html(
                '<button type="button" id="copy-api-key" class="button button-secondary" title="Copiar para área de transferência"><span class="dashicons dashicons-clipboard"></span></button>' +
                '<button type="button" id="toggle-api-key" class="button button-secondary" style="margin-left: 5px;" title="Mostrar/Ocultar chave"><span class="dashicons dashicons-visibility"></span></button>'
            );

            // Função para mostrar mensagem
            function showMessage(message, type) {
                var $messageEl = $('#api-key-message');
                $messageEl.html(message)
                    .removeClass('notice-success notice-error')
                    .addClass('notice-' + type)
                    .show()
                    .delay(3000)
                    .fadeOut();
            }

            // Função para gerar chave
            $('#generate-api-key').on('click', function() {
                // Gerar uma chave aleatória de 32 caracteres
                var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                var key = '';
                for (var i = 0; i < 32; i++) {
                    key += chars.charAt(Math.floor(Math.random() * chars.length));
                }

                // Mudar o tipo do campo para texto para tornar a chave visível
                $('#e1copy_ai_rest_api_key').attr('type', 'text').val(key);

                // Mostrar mensagem de sucesso
                showMessage('<strong>Chave gerada com sucesso!</strong> Clique em "Salvar Configurações" para salvá-la.', 'success');
            });

            // Função para copiar a chave
            $('#copy-api-key').on('click', function() {
                // Garantir que o campo seja visível temporariamente
                var $apiKey = $('#e1copy_ai_rest_api_key');
                var currentType = $apiKey.attr('type');
                $apiKey.attr('type', 'text');

                // Selecionar e copiar
                $apiKey.select();
                document.execCommand('copy');

                // Restaurar tipo original se era password
                if (currentType === 'password') {
                    $apiKey.attr('type', 'password');
                }

                // Mostrar mensagem de sucesso
                showMessage('<strong>Chave copiada!</strong> A chave foi copiada para a área de transferência.', 'success');
            });

            // Botão para alternar visibilidade
            $('#toggle-api-key').on('click', function() {
                var $apiKey = $('#e1copy_ai_rest_api_key');
                var $icon = $(this).find('.dashicons');

                if ($apiKey.attr('type') === 'password') {
                    $apiKey.attr('type', 'text');
                    $icon.removeClass('dashicons-visibility').addClass('dashicons-hidden');
                    $(this).attr('title', 'Ocultar chave');
                } else {
                    $apiKey.attr('type', 'password');
                    $icon.removeClass('dashicons-hidden').addClass('dashicons-visibility');
                    $(this).attr('title', 'Mostrar chave');
                }
            });

            // Adicionar estilos para o campo de mensagem e o modal
            $('<style>')
                .text('#api-key-message { display: inline-block; padding: 5px 10px; border-radius: 3px; } ' +
                      '.notice-success { background-color: #f0f8e6; color: #46b450; border-left: 4px solid #46b450; } ' +
                      '.notice-error { background-color: #fbeaea; color: #dc3232; border-left: 4px solid #dc3232; } ' +
                      '.e1copy-modal { display: none; position: fixed; z-index: 999999; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); } ' +
                      '.e1copy-modal-content { background-color: #fff; margin: 5% auto; padding: 20px; width: 80%; max-width: 1200px; position: relative; max-height: 90vh; overflow-y: auto; } ' +
                      '.e1copy-modal-close { position: absolute; right: 20px; top: 10px; font-size: 28px; cursor: pointer; } ' +
                      '#api-docs-modal .e1copy-modal-content { max-width: 90%; width: 1200px; } ' +
                      '#api-docs-modal .api-docs { padding: 0 40px 60px 40px; } ' +
                      '#api-docs-modal h3 { margin-top: 40px; padding-bottom: 10px; border-bottom: 1px solid #eee; color: #23282d; } ' +
                      '#api-docs-modal h4 { margin-top: 30px; color: #0073aa; } ' +
                      '#api-docs-modal pre { background: #f5f5f5; padding: 15px; border-radius: 4px; overflow: auto; border: 1px solid #e5e5e5; margin: 15px 0; } ' +
                      '#api-docs-modal code { background: #f1f1f1; padding: 2px 5px; border-radius: 3px; font-size: 13px; } ' +
                      '#api-docs-modal ul, #api-docs-modal ol { margin-left: 20px; } ' +
                      '#api-docs-modal li { margin-bottom: 15px; line-height: 1.6; } ' +
                      '#api-docs-modal p { line-height: 1.6; margin-bottom: 15px; } ' +
                      '#api-docs-modal h2 { margin-bottom: 30px; }')
                .appendTo('head');
        });
        </script>





        <?php submit_button(__('Salvar Configurações', 'e1copy-ai')); ?>
    </form>

<script>
var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
jQuery(document).ready(function($) {
    // Toggle mostrar/ocultar chave de ativação
    $('#toggle-activation-key').on('click', function() {
        var input = $('#e1copy_ai_activation_key');
        if (input.attr('type') === 'password') {
            input.attr('type', 'text');
        } else {
            input.attr('type', 'password');
        }
    });

    // Validação em tempo real
    $('#e1copy_ai_activation_key').on('input', function() {
        var key = $(this).val().trim();
        var statusSpan = $('#activation-status');

        if (!key) {
            statusSpan.html('');
            return;
        }

        if (/^e1copy_[a-f0-9]{32}$/.test(key)) {
            statusSpan.html('<span style="color: #00a32a;"><span class="dashicons dashicons-yes-alt"></span> Formato válido</span>');
        } else {
            statusSpan.html('<span style="color: #d63638;"><span class="dashicons dashicons-warning"></span> Formato inválido</span>');
        }
    });

    // Validar chave de ativação
    $('#validate-activation-key').on('click', function() {
        var button = $(this);
        var statusSpan = $('#activation-status');
        var activationKey = $('#e1copy_ai_activation_key').val();

        if (!activationKey) {
            statusSpan.html('<span style="color: #d63638;"><span class="dashicons dashicons-warning"></span> Insira uma chave de ativação</span>');
            return;
        }

        button.prop('disabled', true);
        button.html('<span class="dashicons dashicons-update spin"></span> Validando...');
        statusSpan.html('<span style="color: #666;"><span class="dashicons dashicons-clock"></span> Verificando...</span>');

        // Usar AJAX interno do WordPress
        $.ajax({
            url: ajaxurl,
            method: 'POST',
            data: {
                action: 'e1copy_validate_key',
                activation_key: activationKey,
                nonce: '<?php echo wp_create_nonce('e1copy_ai_validate_key'); ?>'
            },
            timeout: 15000,
            success: function(response) {
                if (response.valid) {
                    statusSpan.html('<span style="color: #00a32a;"><span class="dashicons dashicons-yes-alt"></span> Chave válida e ativa!</span>');
                } else {
                    statusSpan.html('<span style="color: #d63638;"><span class="dashicons dashicons-dismiss"></span> ' + response.message + '</span>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Erro na validação:', error);
                if (status === 'timeout') {
                    statusSpan.html('<span style="color: #d63638;"><span class="dashicons dashicons-warning"></span> Timeout - Verifique sua conexão</span>');
                } else {
                    statusSpan.html('<span style="color: #d63638;"><span class="dashicons dashicons-warning"></span> Erro na validação</span>');
                }
            },
            complete: function() {
                button.prop('disabled', false);
                button.html('<span class="dashicons dashicons-yes"></span> Validar Chave');
            }
        });
    });



    // Validar automaticamente se já houver uma chave
    var existingKey = $('#e1copy_ai_activation_key').val();
    if (existingKey) {
        // Validação básica do formato
        if (/^e1copy_[a-f0-9]{32}$/.test(existingKey)) {
            $('#activation-status').html('<span style="color: #00a32a;"><span class="dashicons dashicons-yes-alt"></span> Chave com formato válido</span>');
        } else {
            $('#activation-status').html('<span style="color: #d63638;"><span class="dashicons dashicons-warning"></span> Formato de chave inválido</span>');
        }
    }
});
</script>

<style>
.activation-key-controls .button {
    vertical-align: middle;
}

.activation-key-controls .dashicons {
    vertical-align: middle;
    margin-right: 5px;
}

#activation-status {
    font-weight: 500;
    vertical-align: middle;
}

#activation-status .dashicons {
    vertical-align: middle;
    margin-right: 3px;
}

.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

    <!-- Modal da Documentação da API -->
    <div id="api-docs-modal" class="e1copy-modal" style="display:none;">
        <div class="e1copy-modal-content">
            <span class="e1copy-modal-close">&times;</span>
            <h2>Documentação da API REST</h2>

            <div class="api-docs">
                <h3>Endpoints disponíveis:</h3>
                <ul>
                    <li>
                        <strong>GET /wp-json/e1copy-ai/v1/test</strong> - Testa se a API está funcionando (não requer autenticação)
                        <br>
                        <small>Use este endpoint para verificar se a API está acessível antes de tentar os outros endpoints.</small>
                    </li>

                    <li>
                        <strong>GET /wp-json/e1copy-ai/v1/verify-key</strong> - Verifica se a chave de API é válida
                        <br>
                        <small>Use este endpoint para testar a autenticação com a chave de API.</small>
                    </li>
                    <li>
                        <strong>GET /wp-json/e1copy-ai/v1/posts</strong> - Lista todos os posts do tipo "Blog Tradicional"
                        <br>
                        <small>Parâmetros opcionais: <code>per_page</code>, <code>page</code>, <code>status</code>, <code>processed</code>, <code>api_key</code> (alternativa ao cabeçalho)</small>
                    </li>
                    <li>
                        <strong>GET /wp-json/e1copy-ai/v1/products</strong> - Lista todos os posts do tipo "Produto Review"
                        <br>
                        <small>Parâmetros opcionais: <code>per_page</code>, <code>page</code>, <code>status</code>, <code>processed</code>, <code>api_key</code> (alternativa ao cabeçalho)</small>
                    </li>
                    <li>
                        <strong>GET /wp-json/e1copy-ai/v1/posts/{id}</strong> - Obtém detalhes de um post específico
                    </li>
                    <li>
                        <strong>POST /wp-json/e1copy-ai/v1/posts/{id}/mark-processed</strong> - Marca um post como processado
                    </li>
                    <li>
                        <strong>POST /wp-json/e1copy-ai/v1/media/upload</strong> - Faz upload de uma imagem para a biblioteca de mídia
                        <br>
                        <small>Aceita upload via arquivo (multipart/form-data), URL externa ou dados Base64</small>
                    </li>
                    <li>
                        <strong>GET /wp-json/e1copy-ai/v1/media</strong> - Lista itens da biblioteca de mídia
                        <br>
                        <small>Parâmetros opcionais: <code>per_page</code>, <code>page</code>, <code>search</code>, <code>mime_type</code></small>
                    </li>
                    <li>
                        <strong>GET /wp-json/e1copy-ai/v1/media/{id}</strong> - Obtém detalhes de um item específico da biblioteca de mídia
                    </li>
                    <li>
                        <strong>POST /wp-json/e1copy-ai/v1/posts/{id}/featured-image</strong> - Define uma imagem destacada para um post
                        <br>
                        <small>Aceita <code>attachment_id</code>, <code>url</code> ou <code>base64_data</code> no corpo da requisição</small>
                    </li>
                </ul>

                <h3>Autenticação:</h3>
                <p>Todas as requisições (exceto o endpoint de teste) requerem autenticação. Você pode usar qualquer um dos métodos abaixo:</p>

                <h4>Autenticação por Chave de API</h4>
                <p>Você pode usar a chave de API de várias formas:</p>
                <ol>
                    <li>Cabeçalho <code>X-E1Copy-API-Key</code> com a chave de API (recomendado)</li>
                    <li>Parâmetro de URL <code>api_key</code> (ex: <code>/wp-json/e1copy-ai/v1/posts?api_key=sua-chave</code>)</li>
                    <li>Cabeçalho <code>Authorization: Bearer sua-chave</code></li>
                </ol>

                <h4>Autenticação Basic Auth</h4>
                <p>Para a API nativa do WordPress (<code>/wp-json/wp/v2/</code>), você também pode usar autenticação Basic Auth:</p>
                <ol>
                    <li>Cabeçalho <code>Authorization: Basic base64(username:password)</code></li>
                    <li>Ou simplesmente forneça seu nome de usuário e senha quando solicitado pelo cliente HTTP</li>
                </ol>
                <p>Exemplo de como gerar o cabeçalho Basic Auth:</p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
# Codificar "username:password" em Base64
echo -n "seu_usuario:sua_senha" | base64
# Resultado: c2V1X3VzdWFyaW86c3VhX3Nlbmhh

# Usar no cabeçalho Authorization
Authorization: Basic c2V1X3VzdWFyaW86c3VhX3Nlbmhh
                </pre>
                <p>Nota: A maioria dos clientes HTTP (como Postman, n8n, etc.) oferece suporte nativo para Basic Auth, então você não precisa gerar o cabeçalho manualmente.</p>

                <p>Exemplo de autenticação com chave de API:</p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
# Usando o cabeçalho X-E1Copy-API-Key
curl -X GET "<?php echo site_url('/wp-json/e1copy-ai/v1/posts'); ?>" \
     -H "X-E1Copy-API-Key: <?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>"

# Usando o parâmetro api_key na URL
curl -X GET "<?php echo site_url('/wp-json/e1copy-ai/v1/posts?api_key='); ?><?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>"

# Usando o cabeçalho Authorization: Bearer
curl -X GET "<?php echo site_url('/wp-json/e1copy-ai/v1/posts'); ?>" \
     -H "Authorization: Bearer <?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>"
                </pre>

                <p>No n8n, você pode configurar a autenticação via cabeçalho nas configurações do nó HTTP Request:</p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// Configuração de autenticação no n8n usando cabeçalho
{
  "url": "<?php echo site_url('/wp-json/e1copy-ai/v1/posts'); ?>",
  "method": "GET",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>"
  }
}
                </pre>

                <h3>Como testar a API:</h3>
                <p>Você pode testar a API usando o seguinte comando curl:</p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
# Testar se a API está funcionando
curl -X GET "<?php echo site_url('/wp-json/e1copy-ai/v1/test'); ?>"

# Listar posts (requer autenticação)
curl -X GET "<?php echo site_url('/wp-json/e1copy-ai/v1/posts'); ?>" -H "X-E1Copy-API-Key: <?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>"
                </pre>

                <h3>Exemplos de uso com n8n:</h3>

                <p><strong>1. Listar todos os posts não processados:</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para listar posts não processados
{
  "url": "<?php echo site_url('/wp-json/e1copy-ai/v1/posts'); ?>",
  "method": "GET",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>"
  }
}
// Nota: A API retorna automaticamente apenas os posts não processados (processed=0)
                </pre>

                <p><strong>2. Obter detalhes de um post específico:</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para obter detalhes de um post
{
  "url": "<?php echo site_url('/wp-json/e1copy-ai/v1/posts/123'); ?>", // Substitua 123 pelo ID do post
  "method": "GET",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>"
  }
}
                </pre>

                <p><strong>3. Remover um post:</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para remover um post
{
  "url": "<?php echo site_url('/wp-json/e1copy-ai/v1/posts/123'); ?>", // Substitua 123 pelo ID do post
  "method": "DELETE",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>"
  }
}
// Nota: Esta ação remove permanentemente o post do banco de dados
                </pre>

                <p><strong>4. Marcar um post como processado (remover da API):</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para marcar um post como processado
{
  "url": "<?php echo site_url('/wp-json/e1copy-ai/v1/posts/123/mark-processed'); ?>", // Substitua 123 pelo ID do post
  "method": "POST",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>"
  }
}
// Nota: Após marcar como processado (processed=1), o post não aparecerá mais na API, mas ainda existe no banco de dados
                </pre>

                <h3>Exemplos de uso da API REST nativa do WordPress:</h3>

                <p><strong>1. Listar posts do WordPress (usando chave API):</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para listar posts do WordPress usando chave API
{
  "url": "<?php echo site_url('/wp-json/wp/v2/posts'); ?>",
  "method": "GET",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>"
  }
}
                </pre>

                <p><strong>1.1. Listar posts do WordPress (usando Basic Auth):</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para listar posts do WordPress usando Basic Auth
{
  "url": "<?php echo site_url('/wp-json/wp/v2/posts'); ?>",
  "method": "GET",
  "authentication": "basicAuth",
  "username": "seu_usuario_wordpress",
  "password": "sua_senha_wordpress"
}
                </pre>

                <p><strong>2. Criar um novo post no WordPress (usando chave API):</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para criar um post no WordPress usando chave API
{
  "url": "<?php echo site_url('/wp-json/wp/v2/posts'); ?>",
  "method": "POST",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>",
    "Content-Type": "application/json"
  },
  "body": {
    "title": "Título do Post",
    "content": "Conteúdo do post...",
    "status": "publish",
    "categories": [5, 7],  // IDs das categorias
    "tags": [10, 12]       // IDs das tags
  }
}
                </pre>

                <p><strong>2.1. Criar um novo post no WordPress (usando Basic Auth):</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para criar um post no WordPress usando Basic Auth
{
  "url": "<?php echo site_url('/wp-json/wp/v2/posts'); ?>",
  "method": "POST",
  "authentication": "basicAuth",
  "username": "seu_usuario_wordpress",
  "password": "sua_senha_wordpress",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "title": "Título do Post",
    "content": "Conteúdo do post...",
    "status": "publish",
    "categories": [5, 7],  // IDs das categorias
    "tags": [10, 12]       // IDs das tags
  }
}
                </pre>

                <p><strong>3. Atualizar um post existente (usando chave API):</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para atualizar um post no WordPress usando chave API
{
  "url": "<?php echo site_url('/wp-json/wp/v2/posts/123'); ?>", // Substitua 123 pelo ID do post
  "method": "POST",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>",
    "Content-Type": "application/json"
  },
  "body": {
    "title": "Título Atualizado",
    "content": "Conteúdo atualizado...",
    "status": "publish"
  }
}
                </pre>

                <p><strong>3.1. Atualizar um post existente (usando Basic Auth):</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para atualizar um post no WordPress usando Basic Auth
{
  "url": "<?php echo site_url('/wp-json/wp/v2/posts/123'); ?>", // Substitua 123 pelo ID do post
  "method": "POST",
  "authentication": "basicAuth",
  "username": "seu_usuario_wordpress",
  "password": "sua_senha_wordpress",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "title": "Título Atualizado",
    "content": "Conteúdo atualizado...",
    "status": "publish"
  }
}
                </pre>

                <h3>Upload de Mídia e Imagens Destacadas:</h3>

                <p>A API oferece endpoints para upload de mídia e definição de imagens destacadas para posts. Você pode fazer upload de imagens de três maneiras diferentes:</p>

                <ol>
                    <li><strong>Via URL externa</strong> - Forneça a URL de uma imagem existente na web</li>
                    <li><strong>Via dados Base64</strong> - Envie a imagem codificada em Base64</li>
                    <li><strong>Via upload de arquivo</strong> - Envie o arquivo binário usando multipart/form-data</li>
                </ol>

                <p><strong>1. Fazer upload de uma imagem via URL:</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para fazer upload de uma imagem via URL
{
  "url": "<?php echo site_url('/wp-json/e1copy-ai/v1/media/upload'); ?>",
  "method": "POST",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>",
    "Content-Type": "application/json"
  },
  "body": {
    "url": "https://exemplo.com/imagem.jpg",
    "title": "Minha Imagem"
  }
}
                </pre>

                <p><strong>2. Fazer upload de uma imagem via dados Base64:</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para fazer upload de uma imagem via Base64
{
  "url": "<?php echo site_url('/wp-json/e1copy-ai/v1/media/upload'); ?>",
  "method": "POST",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>",
    "Content-Type": "application/json"
  },
  "body": {
    "base64_data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
    "filename": "minha-imagem.jpg",
    "title": "Minha Imagem"
  }
}
                </pre>

                <p><strong>3. Fazer upload de uma imagem via arquivo (multipart/form-data) usando chave API:</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// No n8n, você pode usar o nó HTTP Request com a opção "Send Binary Data"
// e conectar um nó anterior que forneça o arquivo binário

// Exemplo de configuração do nó HTTP Request usando chave API:
{
  "url": "<?php echo site_url('/wp-json/e1copy-ai/v1/media/upload'); ?>",
  "method": "POST",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>"
  },
  "sendBinaryData": true,
  "binaryPropertyName": "data", // Nome da propriedade que contém os dados binários
  "formData": {
    "file": {
      "value": "={{ $binary.data }}",
      "options": {
        "filename": "imagem.jpg",
        "contentType": "image/jpeg"
      }
    }
  }
}
                </pre>

                <p><strong>3.1. Fazer upload de uma imagem via API nativa do WordPress usando Basic Auth:</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// Exemplo de configuração do nó HTTP Request usando Basic Auth para a API nativa do WordPress:
{
  "url": "<?php echo site_url('/wp-json/wp/v2/media'); ?>",
  "method": "POST",
  "authentication": "basicAuth",
  "username": "seu_usuario_wordpress",
  "password": "sua_senha_wordpress",
  "sendBinaryData": true,
  "binaryPropertyName": "data", // Nome da propriedade que contém os dados binários
  "formData": {
    "file": {
      "value": "={{ $binary.data }}",
      "options": {
        "filename": "imagem.jpg",
        "contentType": "image/jpeg"
      }
    },
    "title": "Título da imagem",
    "alt_text": "Texto alternativo da imagem"
  }
}
                </pre>

                <p><strong>4. Definir uma imagem destacada para um post:</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// HTTP Request no n8n para definir uma imagem destacada para um post
{
  "url": "<?php echo site_url('/wp-json/e1copy-ai/v1/posts/123/featured-image'); ?>", // Substitua 123 pelo ID do post
  "method": "POST",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>",
    "Content-Type": "application/json"
  },
  "body": {
    "url": "https://exemplo.com/imagem.jpg"
  }
}

// Alternativa usando um ID de anexo existente
{
  "url": "<?php echo site_url('/wp-json/e1copy-ai/v1/posts/123/featured-image'); ?>", // Substitua 123 pelo ID do post
  "method": "POST",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>",
    "Content-Type": "application/json"
  },
  "body": {
    "attachment_id": 456 // Substitua 456 pelo ID do anexo
  }
}
                </pre>

                <h3>Fluxo completo no n8n:</h3>
                <ol>
                    <li>Use o nó "HTTP Request" para buscar posts não processados da API E1Copy</li>
                    <li>Use o nó "Split In Batches" para processar cada post individualmente</li>
                    <li>Se necessário, faça upload de uma imagem usando o endpoint <code>/media/upload</code></li>
                    <li>Use o nó "HTTP Request" com autenticação via chave de API para criar o post no WordPress via API nativa</li>
                    <li>Se necessário, defina uma imagem destacada para o post usando o endpoint <code>/posts/{id}/featured-image</code></li>
                    <li>Use outro nó "HTTP Request" para marcar o post como processado na API E1Copy</li>
                </ol>

                <h3>Autenticação na API REST nativa do WordPress:</h3>
                <p>Você pode usar a mesma chave de API ou Basic Auth para acessar a API REST nativa do WordPress (<code>/wp-json/wp/v2/</code>). Isso permite que você:</p>
                <ul>
                    <li>Liste, crie, atualize e exclua posts, páginas, categorias, tags e outros tipos de conteúdo</li>
                    <li>Gerencie usuários, comentários e configurações do site</li>
                    <li>Acesse todos os endpoints nativos do WordPress e de plugins que usam a API REST</li>
                </ul>

                <h4>Configurando Basic Auth no n8n</h4>
                <p>Para usar Basic Auth no n8n, siga estas etapas:</p>
                <ol>
                    <li>No nó HTTP Request, clique na aba "Authentication"</li>
                    <li>Selecione "Basic Auth" no menu suspenso</li>
                    <li>Preencha os campos "Username" e "Password" com suas credenciais do WordPress</li>
                    <li>Certifique-se de que o usuário tem permissões adequadas (administrador ou editor)</li>
                </ol>
                <p>Exemplo de configuração no código JSON do nó:</p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
{
  "authentication": "basicAuth",
  "username": "seu_usuario_wordpress",
  "password": "sua_senha_wordpress"
}
                </pre>
                <p>Nota: Se você estiver tendo problemas com a autenticação, certifique-se de que está usando um usuário com permissões adequadas (administrador ou editor).</p>

                <p>Para mais informações sobre a API REST nativa do WordPress, consulte a <a href="https://developer.wordpress.org/rest-api/" target="_blank">documentação oficial</a>.</p>

                <h3>Fluxo de trabalho para upload de mídia no n8n</h3>
                <p>Aqui está um exemplo de fluxo de trabalho completo para fazer upload de imagens e criar posts no WordPress:</p>

                <ol>
                    <li><strong>Obter imagem</strong> - Use o nó "HTTP Request" para baixar uma imagem de uma URL ou o nó "Read Binary File" para ler um arquivo local</li>
                    <li><strong>Fazer upload da imagem</strong> - Use o nó "HTTP Request" configurado para enviar dados binários para o endpoint <code>/media/upload</code></li>
                    <li><strong>Criar post</strong> - Use o nó "HTTP Request" para criar um post via API nativa do WordPress</li>
                    <li><strong>Definir imagem destacada</strong> - Use o nó "HTTP Request" para definir a imagem como destacada para o post</li>
                </ol>

                <p><strong>Exemplo de configuração para upload de imagem no n8n:</strong></p>
                <pre style="background: #f5f5f5; padding: 10px; overflow: auto;">
// Nó HTTP Request para fazer upload de imagem
{
  "url": "<?php echo site_url('/wp-json/e1copy-ai/v1/media/upload'); ?>",
  "method": "POST",
  "headers": {
    "X-E1Copy-API-Key": "<?php echo esc_attr(get_option('e1copy_ai_rest_api_key')); ?>"
  },
  "sendBinaryData": true,
  "binaryPropertyName": "data",
  "formData": {
    "file": {
      "value": "={{ $binary.data }}",
      "options": {
        "filename": "={{ $json.filename || 'imagem.jpg' }}",
        "contentType": "={{ $json.mimeType || 'image/jpeg' }}"
      }
    }
  }
}
                </pre>

                <p>Após o upload, você receberá uma resposta com o ID do anexo e a URL da imagem, que pode ser usada para definir a imagem destacada ou inserir no conteúdo do post.</p>

                <h3>Espaço adicional para melhor legibilidade</h3>
                <div style="height: 60px;"></div>
            </div>
        </div>
    </div>

    <script>
    jQuery(document).ready(function($) {
        // Abrir modal da documentação da API
        $('#open-api-docs').on('click', function(e) {
            e.preventDefault();

            // Esconder o spinner global se estiver visível
            if (window.e1copyHideSpinner) {
                window.e1copyHideSpinner();
            } else {
                var overlay = document.getElementById('e1copy-spinner-overlay');
                if (overlay) {
                    overlay.style.display = 'none';
                }
            }

            // Mostrar o modal
            $('#api-docs-modal').show();

            // Impedir rolagem da página de fundo
            $('body').css('overflow', 'hidden');
        });

        // Fechar modal ao clicar no X
        $('.e1copy-modal-close').on('click', function() {
            $('.e1copy-modal').hide();

            // Restaurar rolagem da página
            $('body').css('overflow', 'auto');
        });

        // Fechar modal ao clicar fora do conteúdo
        $('.e1copy-modal').on('click', function(e) {
            if (e.target === this) {
                $(this).hide();
                $('body').css('overflow', 'auto');
            }
        });

        // Fechar modal com a tecla ESC
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27) { // ESC
                $('.e1copy-modal').hide();
                $('body').css('overflow', 'auto');
            }
        });
    });
    </script>
</div>
<?php } ?>