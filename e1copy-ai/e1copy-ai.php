<?php
/**
 * Plugin Name: E1Copy AI
 * Description: <PERSON><PERSON> posts automaticamente usando a API da Groq AI.
 * Version: 1.0
 * Author: <PERSON><PERSON><PERSON>
 */

// Evita acesso direto
if (!defined('ABSPATH')) {
    exit;
}

// Definir constantes do plugin
define('E1COPY_AI_VERSION', '1.0.0');
define('E1COPY_AI_PLUGIN_FILE', __FILE__);

/**
 * Verifica se a chave de ativação é válida
 */
function e1copy_ai_is_activated() {
    $activation_key = get_option('e1copy_ai_activation_key');

    if (empty($activation_key)) {
        return false;
    }

    // Validação básica do formato da chave (aceita 32 ou 64 caracteres hex)
    if (!preg_match('/^e1copy_[a-f0-9]{32,64}$/', $activation_key)) {
        return false;
    }

    // Cache da verificação por 1 hora
    $cache_key = 'e1copy_activation_check_' . md5($activation_key);
    $cached_result = get_transient($cache_key);

    if ($cached_result !== false) {
        return $cached_result === 'valid';
    }

    // Fazer verificação na API
    $response = wp_remote_post('https://app.melhorcupom.shop/api/v1/validate', [
        'timeout' => 10,
        'headers' => [
            'Content-Type' => 'application/json',
            'User-Agent' => 'E1Copy-AI-Plugin/' . E1COPY_AI_VERSION
        ],
        'body' => json_encode([
            'api_key' => $activation_key
        ])
    ]);

    if (is_wp_error($response)) {
        // Em caso de erro de conexão, assumir válido se a chave tem formato correto
        $is_valid = preg_match('/^e1copy_[a-f0-9]{32,64}$/', $activation_key);
        set_transient($cache_key, $is_valid ? 'valid' : 'invalid', HOUR_IN_SECONDS);
        return $is_valid;
    }

    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    $is_valid = isset($data['success']) && $data['success'] &&
                isset($data['data']['valid']) && $data['data']['valid'];

    // Se a API retornou erro mas a chave tem formato válido, assumir válida temporariamente
    if (!$is_valid && preg_match('/^e1copy_[a-f0-9]{32,64}$/', $activation_key)) {
        $is_valid = true;
        set_transient($cache_key, 'valid', 30 * MINUTE_IN_SECONDS); // Cache menor para revalidar
    } else {
        // Cache do resultado por 1 hora
        set_transient($cache_key, $is_valid ? 'valid' : 'invalid', HOUR_IN_SECONDS);
    }

    return $is_valid;
}

/**
 * Exibe aviso se o plugin não estiver ativado
 */
function e1copy_ai_activation_notice() {
    if (!e1copy_ai_is_activated()) {
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>E1Copy AI:</strong> Plugin não ativado. ';
        echo '<a href="' . admin_url('admin.php?page=e1copy-ai-settings') . '">Configure sua chave de ativação</a> ';
        echo 'para liberar todas as funcionalidades.</p>';
        echo '</div>';
    }
}

// Adicionar aviso de ativação
add_action('admin_notices', 'e1copy_ai_activation_notice');

/**
 * Registrar endpoint REST para verificação de status
 */
function e1copy_ai_register_rest_routes() {
    register_rest_route('e1copy/v1', '/status', [
        'methods' => 'GET',
        'callback' => 'e1copy_ai_rest_status',
        'permission_callback' => '__return_true'
    ]);
}

/**
 * Endpoint REST para status do plugin
 */
function e1copy_ai_rest_status() {
    $activation_key = get_option('e1copy_ai_activation_key');
    $is_activated = e1copy_ai_is_activated();

    return [
        'status' => $is_activated ? 'active' : 'inactive',
        'version' => E1COPY_AI_VERSION,
        'site_url' => home_url(),
        'has_activation_key' => !empty($activation_key),
        'plugin_name' => 'E1Copy AI'
    ];
}

// Registrar rotas REST
add_action('rest_api_init', 'e1copy_ai_register_rest_routes');

/**
 * Valida chave de ativação via backend
 */
function e1copy_ai_validate_activation_key($activation_key) {
    if (empty($activation_key)) {
        return ['valid' => false, 'message' => 'Chave de ativação não fornecida'];
    }

    // Validação básica do formato (aceita 32 ou 64 caracteres hex)
    if (!preg_match('/^e1copy_[a-f0-9]{32,64}$/', $activation_key)) {
        return ['valid' => false, 'message' => 'Formato de chave inválido'];
    }

    // Fazer requisição para a API do dashboard
    $response = wp_remote_post('https://app.melhorcupom.shop/api/v1/validate', [
        'timeout' => 15,
        'headers' => [
            'Content-Type' => 'application/json',
            'User-Agent' => 'E1Copy-AI-Plugin/' . E1COPY_AI_VERSION
        ],
        'body' => json_encode([
            'api_key' => $activation_key
        ])
    ]);

    if (is_wp_error($response)) {
        // Se há erro de conexão mas a chave tem formato válido, assumir válida temporariamente
        return [
            'valid' => true,
            'message' => 'Chave aceita (verificação offline) - ' . $response->get_error_message()
        ];
    }

    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    if (!$data) {
        // Se não conseguiu decodificar mas a chave tem formato válido
        return ['valid' => true, 'message' => 'Chave aceita (resposta inválida do servidor)'];
    }

    if (isset($data['success']) && $data['success'] &&
        isset($data['data']['valid']) && $data['data']['valid']) {
        return ['valid' => true, 'message' => 'Chave válida e verificada online'];
    }

    $message = isset($data['message']) ? $data['message'] : 'Chave inválida ou expirada';

    // Se a API disse que é inválida mas tem formato correto, dar uma segunda chance
    if (preg_match('/^e1copy_[a-f0-9]{32,64}$/', $activation_key)) {
        return ['valid' => true, 'message' => 'Chave aceita (formato válido) - ' . $message];
    }

    return ['valid' => false, 'message' => $message];
}

/**
 * Registra o site no dashboard
 */
function e1copy_ai_register_site($activation_key) {
    $response = wp_remote_post('https://app.melhorcupom.shop/api/v1/register-site', [
        'timeout' => 15,
        'headers' => [
            'Content-Type' => 'application/json',
            'User-Agent' => 'E1Copy-AI-Plugin/' . E1COPY_AI_VERSION
        ],
        'body' => json_encode([
            'api_key' => $activation_key,
            'site_url' => home_url(),
            'plugin_version' => E1COPY_AI_VERSION
        ])
    ]);

    if (is_wp_error($response)) {
        error_log('E1Copy AI: Erro ao registrar site - ' . $response->get_error_message());
        return false;
    }

    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    if (isset($data['success']) && $data['success']) {
        error_log('E1Copy AI: Site registrado com sucesso');
        return true;
    }

    error_log('E1Copy AI: Erro ao registrar site - ' . (isset($data['message']) ? $data['message'] : 'Erro desconhecido'));
    return false;
}

/**
 * Verifica se a API está válida e bloqueia funcionalidades se necessário
 */
function e1copy_ai_check_api_access($feature_name = 'funcionalidade') {
    if (!e1copy_ai_is_activated()) {
        wp_die(
            '<div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2 style="color: #d63638;">🚫 Acesso Bloqueado</h2>
                <p style="font-size: 16px; margin: 20px 0;">
                    A licença do E1Copy AI não está válida ou foi suspensa.
                </p>
                <p style="font-size: 14px; color: #666; margin: 20px 0;">
                    Para usar <strong>' . esc_html($feature_name) . '</strong>, você precisa de uma licença válida.
                </p>
                <div style="background: #f0f0f1; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="margin-top: 0;">📋 Como resolver:</h3>
                    <ol style="text-align: left; display: inline-block;">
                        <li>Acesse <a href="https://app.melhorcupom.shop/login" target="_blank"><strong>app.melhorcupom.shop</strong></a></li>
                        <li>Faça login na sua conta</li>
                        <li>Vá em <strong>"Meus Sites"</strong></li>
                        <li>Verifique se sua licença está ativa</li>
                        <li>Se necessário, gere uma nova licença</li>
                        <li>Atualize a licença nas <a href="' . admin_url('admin.php?page=e1copy-ai-settings') . '">configurações do plugin</a></li>
                    </ol>
                </div>
                <p style="margin-top: 30px;">
                    <a href="' . admin_url('admin.php?page=e1copy-ai-settings') . '" class="button button-primary">
                        ⚙️ Ir para Configurações
                    </a>
                    <a href="' . admin_url('admin.php?page=e1copy-ai') . '" class="button" style="margin-left: 10px;">
                        🏠 Voltar ao Dashboard
                    </a>
                </p>
            </div>',
            'E1Copy AI - Licença Inválida',
            ['response' => 403]
        );
    }
}

/**
 * Verifica API via AJAX e retorna JSON
 */
function e1copy_ai_check_api_access_ajax($feature_name = 'funcionalidade') {
    if (!e1copy_ai_is_activated()) {
        wp_send_json_error([
            'message' => 'Licença do E1Copy AI inválida ou suspensa.',
            'feature' => $feature_name,
            'action_required' => 'Verifique sua licença em app.melhorcupom.shop e atualize nas configurações do plugin.'
        ], 403);
    }
}

/**
 * AJAX handler para validação de chave
 */
function e1copy_ai_ajax_validate_key() {
    // Verificar nonce
    if (!wp_verify_nonce($_POST['nonce'], 'e1copy_ai_validate_key')) {
        wp_die('Nonce inválido');
    }

    $activation_key = sanitize_text_field($_POST['activation_key']);

    // Log para debug
    error_log("E1Copy AI: Validando chave via AJAX: " . substr($activation_key, 0, 15) . "...");

    $result = e1copy_ai_validate_activation_key($activation_key);

    // Log do resultado
    error_log("E1Copy AI: Resultado da validação: " . json_encode($result));

    if ($result['valid']) {
        update_option('e1copy_ai_activation_key', $activation_key);
        // Limpar cache de verificação
        $cache_key = 'e1copy_activation_check_' . md5($activation_key);
        delete_transient($cache_key);

        // Registrar site se a chave for válida
        e1copy_ai_register_site($activation_key);

        error_log("E1Copy AI: Chave salva com sucesso");
    }

    wp_send_json($result);
}

// Registrar AJAX handlers
add_action('wp_ajax_e1copy_validate_key', 'e1copy_ai_ajax_validate_key');

// Inclui os arquivos necessários
require_once plugin_dir_path(__FILE__) . 'includes/e1copy-ai-api.php';
require_once plugin_dir_path(__FILE__) . 'includes/screenshot-handler.php';
require_once plugin_dir_path(__FILE__) . 'includes/pexels-api.php';
require_once plugin_dir_path(__FILE__) . 'includes/my-posts-handler.php';
require_once plugin_dir_path(__FILE__) . 'includes/affiliates-handler.php';
require_once plugin_dir_path(__FILE__) . 'includes/assets-handler.php';
// Atualizar a estrutura do banco de dados
require_once plugin_dir_path(__FILE__) . 'includes/db-update.php';

// Incluir autenticação para a API REST nativa do WordPress
require_once plugin_dir_path(__FILE__) . 'includes/wp-api-auth.php';

// Incluir API de mídia
require_once plugin_dir_path(__FILE__) . 'includes/media-api.php';

// Usar a implementação direta da API para garantir respostas JSON válidas
require_once plugin_dir_path(__FILE__) . 'includes/my-posts-api-direct.php';
require_once plugin_dir_path(__FILE__) . 'admin/settings-page.php';
require_once plugin_dir_path(__FILE__) . 'admin/home-page.php';
require_once plugin_dir_path(__FILE__) . 'admin/my-posts-page.php';
require_once plugin_dir_path(__FILE__) . 'admin/affiliates-page.php';
require_once plugin_dir_path(__FILE__) . 'includes/stats-handler.php';

/**
 * Função para redirecionar para a página inicial com o modal de seleção de tipo de post
 */
function e1copy_ai_new_post_page() {
    wp_redirect(admin_url('admin.php?page=e1copy-ai-home&show_modal=1'));
    exit;
}

// Registra os menus no admin
add_action('admin_menu', 'e1copy_ai_register_menus');

// Adiciona hook para processar ações antes de qualquer saída HTML
add_action('admin_init', 'e1copy_ai_process_actions');

// Forçar a atualização das tabelas
add_action('plugins_loaded', 'e1copy_ai_create_my_posts_table');
add_action('plugins_loaded', 'e1copy_ai_create_affiliates_table');

// TEMPORARIAMENTE DESABILITADO: Filtro para processar conteúdo dos posts
// Este filtro estava causando problemas com posts existentes
// add_filter('the_content', 'e1copy_ai_add_affiliate_links_to_images', 10, 1);

// Carrega scripts e estilos apenas nas páginas do plugin
add_action('admin_enqueue_scripts', function($hook) {
    // Verifica se estamos em uma página do plugin
    if (isset($_GET['page']) && strpos($_GET['page'], 'e1copy-ai-') === 0) {
        // Carrega Dashicons apenas nas páginas do plugin
        wp_enqueue_style('dashicons');
    }
}, 1);

function e1copy_ai_process_actions() {
    error_log('E1Copy AI: Entrou na função e1copy_ai_process_actions');
    // Verificar ação de remoção via GET
    if (isset($_GET['page']) && $_GET['page'] === 'e1copy-ai-my-pages' &&
        isset($_GET['action']) && $_GET['action'] === 'delete' &&
        isset($_GET['page_id']) && isset($_GET['_wpnonce'])) {

        $page_id = intval($_GET['page_id']);
        $erro = '';

        // Verificar permissão do usuário
        if (!current_user_can('delete_pages')) {
            $erro = 'Você não tem permissão para remover páginas.';
        }
        // Verificar nonce
        elseif (!wp_verify_nonce($_GET['_wpnonce'], 'delete_page_' . $page_id)) {
            $erro = 'Ação não autorizada (nonce inválido).';
        }
        // Verificar se a página existe e pertence ao plugin
        else {
            $page = get_post($page_id);
            if (!$page || get_post_type($page) !== 'page' || !get_post_meta($page_id, '_e1copy_page_data', true)) {
                $erro = 'Página não encontrada ou não pertence ao plugin.';
            }
        }
        // Se não houver erro, tenta remover
        if (empty($erro)) {
            $result = wp_delete_post($page_id, true);
            if ($result) {
                wp_safe_redirect(admin_url('admin.php?page=e1copy-ai-my-pages&message=deleted'));
                exit;
            } else {
                $erro = 'Erro ao remover a página. Tente novamente.';
            }
        }
        // Se chegou aqui, houve erro
        add_action('admin_notices', function() use ($erro) {
            echo '<div class="notice notice-error is-dismissible"><p><strong>Erro ao remover página:</strong> ' . esc_html($erro) . '</p></div>';
        });
    }
}

function e1copy_ai_register_menus() {
    add_menu_page(
        'E1Copy AI',             // Título da página
        'E1Copy AI',             // Título do menu
        'manage_options',        // Capacidade
        'e1copy-ai-home',        // Slug da página
        'e1copy_ai_home_page',   // Função de callback
        'dashicons-admin-home',  // Ícone
        6                        // Posição
    );

    // Só adicionar submenus se o plugin estiver ativado
    if (!e1copy_ai_is_activated()) {
        // Adicionar apenas configurações se não estiver ativado
        add_submenu_page(
            'e1copy-ai-home',
            'Configurações',
            'Configurações',
            'manage_options',
            'e1copy-ai-settings',
            'e1copy_ai_settings_page'
        );
        return;
    }

    add_submenu_page(
        'e1copy-ai-home',
        'Novo Post',
        'Novo Post',
        'manage_options',
        'e1copy-ai-new-post',
        'e1copy_ai_new_post_page'
    );

    add_submenu_page(
        'e1copy-ai-home',
        'Clonar Página',
        'Clonar Página',
        'manage_options',
        'e1copy-ai-screenshot',
        'e1copy_ai_screenshot_page'
    );

    // Adicionar Minhas Páginas como submenu
    add_submenu_page(
        'e1copy-ai-home',        // Menu pai
        'Minhas Páginas',        // Título da página
        'Minhas Páginas',        // Título do menu
        'manage_options',        // Capacidade necessária
        'e1copy-ai-my-pages',    // Slug da página
        'e1copy_ai_my_pages_page' // Função de callback
    );

    // Adicionar Meus Posts como submenu
    add_submenu_page(
        'e1copy-ai-home',        // Menu pai
        'Meus Posts',            // Título da página
        'Meus Posts',            // Título do menu
        'manage_options',        // Capacidade necessária
        'e1copy-ai-my-posts',    // Slug da página
        'e1copy_ai_my_posts_page' // Função de callback
    );

    // Adicionar Meus Afiliados como submenu
    add_submenu_page(
        'e1copy-ai-home',        // Menu pai
        'Meus Afiliados',        // Título da página
        'Meus Afiliados',        // Título do menu
        'manage_options',        // Capacidade necessária
        'e1copy-ai-affiliates',  // Slug da página
        'e1copy_ai_affiliates_page' // Função de callback
    );

    // Mover Configurações para o final
    add_submenu_page(
        'e1copy-ai-home',
        'Configurações da API',
        'Configurações',
        'manage_options',
        'e1copy-ai-settings',
        'e1copy_ai_settings_page'
    );
}

// Função para exibir a página de gerenciamento
function e1copy_ai_my_pages_page() {
    // Verificar mensagens
    if (isset($_GET['message'])) {
        $messages = [
            'created' => 'Página criada com sucesso!',
            'updated' => 'Página atualizada com sucesso!',
            'deleted' => 'Página excluída com sucesso!'
        ];

        if (isset($messages[$_GET['message']])) {
            echo '<div class="notice notice-success is-dismissible"><p>' . esc_html($messages[$_GET['message']]) . '</p></div>';
        }
    }

    // Verificar ação de atualização
    if (isset($_POST['action']) && $_POST['action'] === 'update' && isset($_POST['page_id'])) {
        if (! isset($_POST['_wpnonce']) || ! wp_verify_nonce($_POST['_wpnonce'], 'e1copy_update_page')) {
            wp_die('Ação não autorizada.');
        }
        $page_id = intval($_POST['page_id']);

        // Atualizar dados da página
        $data = [
            'url' => isset($_POST['url']) ? esc_url_raw(is_array($_POST['url']) ? $_POST['url'][0] : $_POST['url']) : '',
            'page_title' => isset($_POST['page_title']) ? sanitize_text_field(is_array($_POST['page_title']) ? $_POST['page_title'][0] : $_POST['page_title']) : '',
            'redirect_url' => isset($_POST['redirect_url']) ? esc_url_raw(is_array($_POST['redirect_url']) ? $_POST['redirect_url'][0] : $_POST['redirect_url']) : '',
            'auto_redirect' => isset($_POST['auto_redirect']) ? sanitize_text_field(is_array($_POST['auto_redirect']) ? $_POST['auto_redirect'][0] : $_POST['auto_redirect']) : 'no',
            'redirect_delay' => isset($_POST['redirect_delay']) ? intval(is_array($_POST['redirect_delay']) ? $_POST['redirect_delay'][0] : $_POST['redirect_delay']) : 5,
            'cookie_text' => isset($_POST['cookie_text']) ? wp_kses_post(is_array($_POST['cookie_text']) ? $_POST['cookie_text'][0] : $_POST['cookie_text']) : '',
            'button_color' => isset($_POST['button_color']) ? sanitize_hex_color(is_array($_POST['button_color']) ? $_POST['button_color'][0] : $_POST['button_color']) : '#4285F4',
            'post_status' => isset($_POST['post_status']) && in_array(is_array($_POST['post_status']) ? $_POST['post_status'][0] : $_POST['post_status'], ['draft', 'publish']) ? (is_array($_POST['post_status']) ? $_POST['post_status'][0] : $_POST['post_status']) : 'draft',
        ];

        // Capturar novos screenshots somente se a URL mudou
        $original_meta = get_post_meta($page_id, '_e1copy_page_data', true);
        $original_url = isset($original_meta['url']) ? $original_meta['url'] : '';
        if ($data['url'] !== $original_url) {
            // Sempre captura e salva em base64
            $desktop = e1copy_ai_capture_screenshot($data['url'], 'desktop');
            $mobile = e1copy_ai_capture_screenshot($data['url'], 'mobile');
            if (is_wp_error($desktop) || is_wp_error($mobile)) {
                echo '<div class="notice notice-error"><p>Erro ao capturar screenshots para a nova URL.</p></div>';
                return;
            } else {
                $data['desktop_url'] = $desktop;
                $data['mobile_url'] = $mobile;
            }
        } else {
            // Reutilizar screenshots existentes se a URL não mudou
            $data['desktop_url'] = isset($original_meta['desktop_url']) ? $original_meta['desktop_url'] : '';
            $data['mobile_url'] = isset($original_meta['mobile_url']) ? $original_meta['mobile_url'] : '';
        }

        if (!is_wp_error($data['desktop_url']) && !is_wp_error($data['mobile_url'])) {
            // Atualizar o conteúdo da página com os novos dados
            $html = e1copy_ai_generate_page_html($data);

            // Gerar novo slug baseado no título
            $new_slug = sanitize_title($data['page_title']);

            wp_update_post([
                'ID' => $page_id,
                'post_title' => $data['page_title'],
                'post_name' => $new_slug, // Atualiza o slug
                'post_content' => $html,
                'post_status' => $data['post_status'],
            ]);

            // Salvar metadados
            update_post_meta($page_id, '_e1copy_page_data', $data);

            echo '<div class="notice notice-success"><p>Página atualizada com sucesso!</p></div>';
        }
    }

    // Verificar ação de remoção via POST
    if (isset($_POST['action']) && $_POST['action'] === 'delete' && isset($_POST['page_id'])) {
        $page_id = intval($_POST['page_id']);

        // Log para depuração
        error_log('E1Copy AI: Tentativa de remoção da página ID: ' . $page_id . ' (via POST)');

        // Verificar nonce
        if (!isset($_POST['_wpnonce']) || !wp_verify_nonce($_POST['_wpnonce'], 'delete_page_' . $page_id)) {
            error_log('E1Copy AI: Nonce inválido para remoção da página ID: ' . $page_id);
            wp_die('Ação não autorizada.');
        }

        // Verificar se a página existe e pertence ao plugin
        $page = get_post($page_id);
        if (!$page || get_post_type($page) !== 'page' || !get_post_meta($page_id, '_e1copy_page_data', true)) {
            error_log('E1Copy AI: Página não encontrada ou não pertence ao plugin. ID: ' . $page_id);
            wp_die('Página não encontrada ou não pertence ao plugin.');
        }

        // Remover a página
        error_log('E1Copy AI: Tentando remover a página ID: ' . $page_id);
        $result = wp_delete_post($page_id, true);

        if ($result) {
            error_log('E1Copy AI: Página removida com sucesso. ID: ' . $page_id);
            // Redirecionar com mensagem de sucesso
            $redirect_url = admin_url('admin.php?page=e1copy-ai-my-pages&message=deleted');
            error_log('E1Copy AI: Redirecionando para: ' . $redirect_url);
            wp_safe_redirect($redirect_url);
            exit;
        } else {
            error_log('E1Copy AI: Erro ao remover a página. ID: ' . $page_id);
            wp_die('Erro ao remover a página.');
        }
    }

    // Buscar páginas criadas pelo plugin
    $args = [
        'post_type'      => 'page',
        'post_status'    => ['draft', 'publish'],
        'posts_per_page' => -1,
        'meta_query' => [
            [
                'key' => '_e1copy_page_data',
                'compare' => 'EXISTS'
            ]
        ]
    ];

    $pages = get_posts($args);
    ?>
    <div class="wrap">
        <h1>Minhas Páginas</h1>

        <?php if (empty($pages)): ?>
            <div class="notice notice-info">
                <p>Nenhuma página encontrada. <a href="<?php echo admin_url('admin.php?page=e1copy-ai-screenshot'); ?>">Criar nova página</a></p>
            </div>
        <?php else: ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Título</th>
                        <th>URL Original</th>
                        <th>Data de Criação</th>
                        <th>Data de Atualização</th>
                        <th>Redirecionamento</th>
                        <th>Link ADS</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pages as $page):
                        $page_data = get_post_meta($page->ID, '_e1copy_page_data', true);
                        $status = get_post_status($page->ID);
                        $permalink = get_permalink($page->ID);
                    ?>
                    <tr>
                        <td style="text-align:center;">
                            <?php if ($status === 'publish'): ?>
                                <span style="color:green;font-size:20px;" title="Publicado">&#10004;</span>
                            <?php elseif ($status === 'draft'): ?>
                                <span style="color:orange;font-size:20px;" title="Rascunho">&#9888;</span>
                            <?php else: ?>
                                <span style="color:gray;font-size:20px;" title="Outro">&#9675;</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo esc_html($page->post_title); ?></td>
                        <td><?php echo esc_url($page_data['url']); ?></td>
                        <td><?php echo get_the_date('d/m/Y H:i', $page->ID); ?></td>
                        <td><?php echo get_the_modified_date('d/m/Y H:i', $page->ID); ?></td>
                        <td>
                            <?php
                            if (!empty($page_data['redirect_url'])) {
                                echo '<a href="' . esc_url($page_data['redirect_url']) . '" target="_blank">' . esc_html($page_data['redirect_url']) . '</a>';
                            } else {
                                echo '<em>—</em>';
                            }
                            ?>
                        </td>
                        <td style="text-align:center;">
                            <button class="copy-ads-link" data-link="<?php echo esc_url($permalink); ?>" title="Copiar link" style="background:none;border:none;cursor:pointer;font-size:18px;">
                                &#128203;
                            </button>
                        </td>
                        <td style="text-align:center; white-space:nowrap;">
                            <a href="#" class="action-icon edit-page" data-page-id="<?php echo $page->ID; ?>" title="Editar" style="margin-right:8px; font-size:18px; color:#4285F4; text-decoration:none; display:inline-block;">
                                <span class="dashicons dashicons-edit"></span>
                            </a>
                            <a href="<?php echo get_permalink($page->ID); ?>" class="action-icon" target="_blank" title="Visualizar" style="margin-right:8px; font-size:18px; color:#4285F4; text-decoration:none; display:inline-block;">
                                <span class="dashicons dashicons-visibility"></span>
                            </a>
                            <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=e1copy-ai-my-pages&action=delete&page_id=' . $page->ID), 'delete_page_' . $page->ID); ?>" class="action-icon button-link-delete" title="Remover" style="font-size:18px; color:#d93025; text-decoration:none; display:inline-block;" onclick="return confirm('Tem certeza que deseja excluir esta página?')">
                                <span class="dashicons dashicons-trash"></span>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>

        <!-- Modal de Edição -->
        <div id="edit-page-modal" class="e1copy-modal" style="display:none;">
            <div class="e1copy-modal-content">
                <span class="e1copy-modal-close">&times;</span>
                <h2>Editar Página</h2>
                <form method="post" class="edit-page-form">
                    <?php wp_nonce_field('e1copy_update_page', '_wpnonce'); ?>
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="page_id" value="">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><label for="url">URL do Site</label></th>
                            <td>
                                <input type="url" name="url" id="url" required class="regular-text">
                                <p class="description">Digite a URL completa (ex: https://exemplo.com)</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="page_title">Título da Página</label></th>
                            <td>
                                <input type="text" name="page_title" id="page_title" required class="regular-text">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="redirect_url">URL de Redirecionamento</label></th>
                            <td>
                                <input type="url" name="redirect_url" id="redirect_url" required class="regular-text">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label>Redirecionamento Automático</label></th>
                            <td>
                                <label><input type="radio" name="auto_redirect" value="yes"> Sim</label>
                                <label><input type="radio" name="auto_redirect" value="no" checked> Não</label>
                                <div id="redirect_delay_container" style="margin-top:10px;display:none;">
                                    <select name="redirect_delay">
                                        <option value="5">5 segundos</option>
                                        <option value="10">10 segundos</option>
                                        <option value="20">20 segundos</option>
                                    </select>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="cookie_text">Texto do Popup de Cookies</label></th>
                            <td>
                                <?php
                                wp_editor(
                                    '<h2>Este site usa cookies</h2>Este site usa cookies para personalizar conteúdo e anúncios, fornecer recursos de mídia social e analisar nosso tráfego. Ao clicar em Aceitar, você concorda com o uso de cookies. Para mais informações, por favor visite nossa <a href="#">Política de Cookies</a>.',
                                    'cookie_text',
                                    [
                                        'textarea_name' => 'cookie_text',
                                        'textarea_rows' => 5,
                                        'media_buttons' => false,
                                        'teeny'         => true,
                                        'quicktags'     => true
                                    ]
                                );
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="button_color">Cor dos Botões/Link (HEX)</label></th>
                            <td>
                                <input type="text" name="button_color" id="button_color" class="color-field" value="#4285F4">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="post_status">Status da Página</label></th>
                            <td>
                                <select name="post_status" id="post_status" class="regular-text">
                                    <option value="draft">Rascunho</option>
                                    <option value="publish">Publicada</option>
                                </select>
                                <p class="description">Escolha se a página ficará como rascunho ou publicada.</p>
                            </td>
                        </tr>
                    </table>
                    <div class="preview-container">
                        <h3>Preview</h3>
                        <div class="preview-images">
                            <div class="desktop-preview">
                                <h4>
                                    <span class="dashicons dashicons-desktop" style="vertical-align:middle;margin-right:6px;"></span>
                                    Versão Desktop
                                </h4>
                                <img id="desktopImg" src="<?php echo $data['desktop_url']; ?>" '
                                   . 'style="display:block;width:100%;height:100%;object-fit:cover">';
                            </div>
                            <div class="mobile-preview">
                                <h4>
                                    <span class="dashicons dashicons-smartphone" style="vertical-align:middle;margin-right:6px;"></span>
                                    Versão Mobile
                                </h4>
                                <img id="mobileImg" src="<?php echo $data['mobile_url']; ?>" '
                                   . 'style="display:none;width:100%;height:100%;object-fit:cover">';
                            </div>
                        </div>
                    </div>
                    <p class="submit">
                        <button type="submit" class="button button-primary">Salvar Alterações</button>
                        <span id="e1copy-edit-spinner" style="display:none;vertical-align:middle;margin-left:10px;">
                            <span class="spinner is-active" style="float:none;display:inline-block;"></span>
                        </span>
                    </p>
                </form>
            </div>
        </div>

        <style>
        .e1copy-modal {
            display: none;
            position: fixed;
            z-index: 999999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .e1copy-modal-content {
            background-color: #fff;
            margin: 5% auto;
            padding: 20px;
            width: 80%;
            max-width: 1200px;
            position: relative;
            max-height: 90vh;
            overflow-y: auto;
        }
        .e1copy-modal-close {
            position: absolute;
            right: 20px;
            top: 10px;
            font-size: 28px;
            cursor: pointer;
        }
        .preview-container {
            margin-top: 20px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        .preview-images {
            display: flex;
            gap: 20px;
        }
        .preview-images > div {
            flex: 1;
        }
        .preview-images img {
            max-width: 100%;
            border: 1px solid #ddd;
        }
        .action-icon .dashicons {
            vertical-align: middle;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            $('.edit-page').off('click').on('click', function(e) {
                e.preventDefault();
                var pageId = $(this).data('page-id');
                $.post(ajaxurl, {
                    action: 'e1copy_get_page_data',
                    page_id: pageId,
                    nonce: '<?php echo wp_create_nonce("e1copy_get_page_data"); ?>'
                }, function(response) {
                    if (response.success) {
                        var data = response.data;
                        $('#edit-page-modal form [name="page_id"]').val(pageId);
                        $('#edit-page-modal form [name="url"]').val(data.url);
                        $('#edit-page-modal form [name="page_title"]').val(data.page_title);
                        $('#edit-page-modal form [name="redirect_url"]').val(data.redirect_url);
                        $('#edit-page-modal form [name="auto_redirect"][value="' + data.auto_redirect + '"]').prop('checked', true);
                        $('#edit-page-modal form [name="redirect_delay"]').val(data.redirect_delay);
                        $('#edit-page-modal form [name="button_color"]').val(data.button_color).change();
                        if (data.post_status) {
                            $('#edit-page-modal form [name="post_status"]').val(data.post_status).trigger('change');
                        }
                        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('cookie_text')) {
                            tinyMCE.get('cookie_text').setContent(data.cookie_text);
                        }
                        $('#edit-page-modal form [name="cookie_text"]').val(data.cookie_text);
                        $('.desktop-preview img').attr('src', data.desktop_url);
                        $('.mobile-preview img').attr('src', data.mobile_url);
                        $('#edit-page-modal').show();
                    } else {
                        alert('Erro ao buscar dados da página para edição.');
                    }
                });
            });

            $('.e1copy-modal-close').click(function() {
                $('#edit-page-modal').hide();
            });

            $(window).click(function(e) {
                if ($(e.target).hasClass('e1copy-modal')) {
                    $('.e1copy-modal').hide();
                }
            });

            $('input[name="auto_redirect"]').change(function() {
                $('#redirect_delay_container').toggle($(this).val() === 'yes');
            });

            // Mostrar spinner ao enviar o formulário de edição
            $('#edit-page-modal form.edit-page-form').on('submit', function() {
                $('#e1copy-edit-spinner').show();
            });
        });
        </script>

        <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.copy-ads-link').forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    var link = btn.getAttribute('data-link');
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(link).then(function() {
                            btn.innerHTML = '✔️';
                            setTimeout(function() { btn.innerHTML = '📋'; }, 1200);
                        });
                    } else {
                        // Fallback para browsers antigos
                        var temp = document.createElement('input');
                        document.body.appendChild(temp);
                        document.body.removeChild(temp);
                        btn.innerHTML = '✔️';
                        setTimeout(function() { btn.innerHTML = '📋'; }, 1200);
                    }
                });
            });
            // Esconder spinner ao cancelar exclusão
            document.querySelectorAll('.action-icon.button-link-delete').forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    // Mostra o spinner
                    var overlay = document.getElementById('e1copy-spinner-overlay');
                    if (overlay) overlay.style.display = 'flex';
                    // Confirmação customizada
                    var confirmed = confirm('Tem certeza que deseja excluir esta página?');
                    if (!confirmed) {
                        // Se cancelar, esconde o spinner
                        if (overlay) overlay.style.display = 'none';
                        e.preventDefault();
                    }
                    // Se confirmar, segue o fluxo normal
                });
            });
        });
        </script>
    </div>
    <?php
}

// Excluir páginas da listagem padrão do WordPress e dos resultados de pesquisa
add_action('pre_get_posts', 'e1copy_ai_exclude_pages_from_listing');
function e1copy_ai_exclude_pages_from_listing($query) {
    global $pagenow;

    // Para admin: excluir da listagem de páginas do WordPress
    if (is_admin()) {
        // Verificar se estamos na listagem de páginas do WordPress
        if ($pagenow == 'edit.php' && isset($query->query['post_type']) && $query->query['post_type'] == 'page') {
            // Não aplicar o filtro se estivermos na página do nosso plugin
            if (isset($_GET['page']) && $_GET['page'] == 'e1copy-ai-my-pages') {
                return;
            }

            // Configurar meta query para excluir nossas páginas
            $meta_query = $query->get('meta_query');
            if (!is_array($meta_query)) {
                $meta_query = array();
            }

            $meta_query[] = array(
                'key' => '_e1copy_page_data',
                'compare' => 'NOT EXISTS'
            );

            $query->set('meta_query', $meta_query);
        }
        return;
    }

    // Para frontend: excluir das pesquisas e consultas públicas
    if (!$query->is_main_query()) {
        return;
    }

    // Excluir das pesquisas, feeds RSS e consultas de páginas
    if ($query->is_search() || $query->is_feed() ||
        ($query->is_home() && $query->get('post_type') == 'page') ||
        ($query->get('post_type') == 'page' && !$query->is_singular())) {

        // Configurar meta query para excluir nossas páginas
        $meta_query = $query->get('meta_query');
        if (!is_array($meta_query)) {
            $meta_query = array();
        }

        $meta_query[] = array(
            'key' => '_e1copy_page_data',
            'compare' => 'NOT EXISTS'
        );

        $query->set('meta_query', $meta_query);
    }
}

// Excluir páginas do plugin dos sitemaps XML (WordPress 5.5+)
add_filter('wp_sitemaps_posts_query_args', 'e1copy_ai_exclude_from_sitemap', 10, 2);
function e1copy_ai_exclude_from_sitemap($args, $post_type) {
    if ($post_type === 'page') {
        if (!isset($args['meta_query'])) {
            $args['meta_query'] = array();
        }

        $args['meta_query'][] = array(
            'key' => '_e1copy_page_data',
            'compare' => 'NOT EXISTS'
        );
    }
    return $args;
}

// Adicionar meta tag noindex para páginas do plugin
add_action('wp_head', 'e1copy_ai_add_noindex_meta');
function e1copy_ai_add_noindex_meta() {
    if (is_page()) {
        global $post;
        if ($post && get_post_meta($post->ID, '_e1copy_page_data', true)) {
            echo '<meta name="robots" content="noindex, nofollow, noarchive, nosnippet">' . "\n";
        }
    }
}

// Excluir das consultas de widgets e outras consultas secundárias
add_action('pre_get_posts', 'e1copy_ai_exclude_from_widgets');
function e1copy_ai_exclude_from_widgets($query) {
    // Não aplicar no admin ou na query principal
    if (is_admin() || $query->is_main_query()) {
        return;
    }

    // Aplicar apenas para consultas de páginas
    if ($query->get('post_type') == 'page' ||
        (empty($query->get('post_type')) && $query->is_search())) {

        $meta_query = $query->get('meta_query');
        if (!is_array($meta_query)) {
            $meta_query = array();
        }

        $meta_query[] = array(
            'key' => '_e1copy_page_data',
            'compare' => 'NOT EXISTS'
        );

        $query->set('meta_query', $meta_query);
    }
}

// Excluir das consultas do Yoast SEO e outros plugins de SEO
add_filter('wpseo_exclude_from_sitemap_by_post_ids', 'e1copy_ai_exclude_from_yoast_sitemap');
function e1copy_ai_exclude_from_yoast_sitemap() {
    global $wpdb;

    $excluded_ids = $wpdb->get_col(
        "SELECT post_id FROM {$wpdb->postmeta}
         WHERE meta_key = '_e1copy_page_data'"
    );

    return $excluded_ids;
}

// Excluir do RankMath SEO sitemap
add_filter('rank_math/sitemap/exclude_post', 'e1copy_ai_exclude_from_rankmath_sitemap', 10, 2);
function e1copy_ai_exclude_from_rankmath_sitemap($exclude, $post) {
    if ($post->post_type === 'page' && get_post_meta($post->ID, '_e1copy_page_data', true)) {
        return true;
    }
    return $exclude;
}

// Filtro adicional para garantir que as páginas não apareçam em nenhuma listagem pública
add_filter('get_pages', 'e1copy_ai_filter_get_pages');
function e1copy_ai_filter_get_pages($pages) {
    if (is_admin()) {
        return $pages;
    }

    return array_filter($pages, function($page) {
        return !get_post_meta($page->ID, '_e1copy_page_data', true);
    });
}

// Adicionar mensagens de status
function e1copy_ai_admin_notices() {
    if (isset($_GET['page']) && $_GET['page'] == 'e1copy-ai-my-pages') {
        if (isset($_GET['message'])) {
            $message = '';
            $type = 'success';

            switch ($_GET['message']) {
                case 'created':
                    $message = 'Página criada com sucesso!';
                    break;
                case 'updated':
                    $message = 'Página atualizada com sucesso!';
                    break;
                case 'deleted':
                    $message = 'Página excluída com sucesso!';
                    break;
            }

            if ($message) {
                printf('<div class="notice notice-%s is-dismissible"><p>%s</p></div>',
                    esc_attr($type),
                    esc_html($message)
                );
            }
        }
    }
}
add_action('admin_notices', 'e1copy_ai_admin_notices');

/**
 * Adiciona links de afiliados às imagens dos posts criados pelo plugin
 *
 * Esta função é executada automaticamente no frontend para posts específicos.
 * Ela identifica posts criados pelo plugin E1Copy AI e adiciona automaticamente
 * o link do afiliado associado ao redor de todas as imagens do post.
 *
 * Como funciona:
 * 1. Verifica se o post foi criado pelo plugin (usando meta dados)
 * 2. Obtém o afiliado associado ao post
 * 3. Adiciona o link do afiliado ao redor de todas as imagens
 *
 * @param string $content O conteúdo do post
 * @return string O conteúdo processado com links de afiliados nas imagens
 */
function e1copy_ai_add_affiliate_links_to_images($content) {
    // Verificar se estamos no frontend e se é um post singular
    if (is_admin() || !is_singular('post')) {
        return $content;
    }

    global $post;
    if (!$post) {
        return $content;
    }

    // PRIMEIRA VERIFICAÇÃO: Verificar se o post foi criado pelo plugin usando meta dados
    // Isso é mais eficiente e confiável do que consultas no banco de dados
    $plugin_post_id = get_post_meta($post->ID, '_e1copy_plugin_post_id', true);
    $created_by_plugin = get_post_meta($post->ID, '_e1copy_created_by_plugin', true);

    // Se não tem os meta dados do plugin, não é um post criado pelo plugin
    if (!$plugin_post_id || !$created_by_plugin) {
        return $content;
    }

    // SEGUNDA VERIFICAÇÃO: Buscar dados do post na tabela do plugin
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_my_posts';

    // Buscar pelo ID específico do plugin
    $plugin_post = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND post_type = 'product'",
            intval($plugin_post_id)
        ),
        ARRAY_A
    );

    // Se não encontrou o post na tabela do plugin, retornar conteúdo original
    if (!$plugin_post) {
        return $content;
    }

    // Verificar se o post tem um afiliado associado
    if (empty($plugin_post['affiliate_id']) || intval($plugin_post['affiliate_id']) <= 0) {
        return $content;
    }

    // Obter dados do afiliado
    $affiliate_id = intval($plugin_post['affiliate_id']);
    $affiliate = e1copy_ai_get_affiliate($affiliate_id);

    if (!$affiliate || empty($affiliate['affiliate_link'])) {
        return $content;
    }

    $affiliate_link = esc_url($affiliate['affiliate_link']);

    // Processar o conteúdo para adicionar links às imagens
    $processed_content = e1copy_ai_process_content_images($content, $affiliate_link);

    return $processed_content;
}

/**
 * Processa o conteúdo HTML para adicionar links de afiliados às imagens
 */
function e1copy_ai_process_content_images($content, $affiliate_link) {
    // Verificar se o conteúdo e o link do afiliado são válidos
    if (empty($content) || empty($affiliate_link)) {
        return $content;
    }

    // Verificar se o conteúdo já foi processado (evitar processamento duplo)
    if (strpos($content, 'data-e1copy-processed="true"') !== false) {
        return $content;
    }

    try {
        // Usar DOMDocument para processamento mais seguro do HTML
        $dom = new DOMDocument();
        $dom->loadHTML('<?xml encoding="utf-8" ?>' . $content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        $images = $dom->getElementsByTagName('img');
        $images_to_process = [];

        // Coletar imagens que não estão dentro de links
        foreach ($images as $img) {
            $parent = $img->parentNode;
            $is_inside_link = false;

            // Verificar se a imagem já está dentro de um link
            while ($parent) {
                if ($parent->nodeName === 'a') {
                    $is_inside_link = true;
                    break;
                }
                $parent = $parent->parentNode;
            }

            if (!$is_inside_link) {
                $images_to_process[] = $img;
            }
        }

        // Processar as imagens coletadas
        foreach ($images_to_process as $img) {
            $link = $dom->createElement('a');
            $link->setAttribute('href', $affiliate_link);
            $link->setAttribute('target', '_blank');
            $link->setAttribute('rel', 'nofollow');
            $link->setAttribute('style', 'text-decoration: none; color: inherit;');

            // Substituir a imagem pelo link contendo a imagem
            $img->parentNode->replaceChild($link, $img);
            $link->appendChild($img);
        }

        // Adicionar marcador para evitar processamento duplo
        $marker = $dom->createElement('span');
        $marker->setAttribute('data-e1copy-processed', 'true');
        $marker->setAttribute('style', 'display: none;');
        $dom->appendChild($marker);

        $processed_content = $dom->saveHTML();

        // Limpar o XML encoding adicionado
        $processed_content = str_replace('<?xml encoding="utf-8" ?>', '', $processed_content);

        return $processed_content;

    } catch (Exception $e) {
        // Se houver erro no processamento DOM, usar método de fallback mais simples
        error_log('E1Copy AI: Erro no processamento DOM: ' . $e->getMessage());

        // Fallback: usar regex mais simples e segura
        $pattern = '/<img([^>]*?)>/i';

        $callback = function($matches) use ($affiliate_link) {
            $img_tag = $matches[0];

            // Verificar se a imagem já está dentro de um link (verificação simples)
            $context_before = substr($content, max(0, strpos($content, $img_tag) - 100), 100);
            if (preg_match('/<a[^>]*>(?!.*<\/a>)/', $context_before)) {
                return $img_tag; // Provavelmente já está dentro de um link
            }

            // Criar o link do afiliado ao redor da imagem
            return '<a href="' . esc_url($affiliate_link) . '" target="_blank" rel="nofollow" style="text-decoration: none; color: inherit;">' . $img_tag . '</a>';
        };

        return preg_replace_callback($pattern, $callback, $content);
    }
}

/**
 * Adiciona meta dados aos posts criados via API para identificação
 */
function e1copy_ai_mark_post_as_plugin_created($post_id, $plugin_post_id) {
    if ($post_id && $plugin_post_id) {
        update_post_meta($post_id, '_e1copy_plugin_post_id', $plugin_post_id);
        update_post_meta($post_id, '_e1copy_created_by_plugin', true);
    }
}

// Função de verificação removida para evitar interferência com posts existentes

// Gera o HTML completo da página a partir dos dados do metadado
if ( ! function_exists('e1copy_ai_generate_page_html') ) {
    function e1copy_ai_generate_page_html(array $data) {
        $html  = '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>'
               . esc_html($data['page_title'])
               . '</title>';
        $html .= '<style>body{margin:0;padding:0;overflow:hidden}</style>';
        $html .= '</head><body>';
        // Sombra
        $html .= '<div style="position:fixed;top:0;left:0;width:100%;height:100%;'
               . 'background:rgba(0,0,0,0.7)"></div>';
        // Imagens
        $html .= '<img id="desktopImg" src="' . $data['desktop_url'] . '" '
               . 'style="display:block;width:100%;height:100%;object-fit:cover">';
        $html .= '<img id="mobileImg" src="' . $data['mobile_url'] . '" '
               . 'style="display:none;width:100%;height:100%;object-fit:cover">';
        // Script de switch
        $html .= '<script>
            if(window.matchMedia("(max-width:768px)").matches) {
                document.getElementById("desktopImg").style.display = "none";
                document.getElementById("mobileImg").style.display = "block";
            }
        </script>';
        // Redirecionamento automático por tempo
        if ( $data['auto_redirect'] === 'yes' && ! empty($data['redirect_url']) ) {
            $delay  = intval($data['redirect_delay']) * 1000;
            $html  .= '<script>
                setTimeout(function(){
                    window.location.href = "' . esc_url($data['redirect_url']) . '";
                }, ' . $delay . ');
            </script>';
        }
        // Redirecionamento por clique em qualquer lugar da página
        if ( $data['auto_redirect'] === 'yes' && ! empty($data['redirect_url']) ) {
            $html .= '<script>
                document.addEventListener("click", function() {
                    window.location.href = "' . esc_url($data['redirect_url']) . '";
                });
            </script>';
        }
        // Popup cookies
        $html .= '<div id="cookie-consent" style="position:fixed;bottom:20px;left:50%;transform:translateX(-50%);background:#fff;padding:20px 30px;border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,0.2);z-index:9999;display:block;max-width:500px;text-align:left;">'
               . wp_kses_post($data['cookie_text']) .
               '<button id="accept-cookie" style="margin-top:10px;background:' . esc_attr($data['button_color']) . ';color:#fff;border:none;padding:8px 16px;border-radius:4px;cursor:pointer;">Aceitar</button>
        </div>
        <script>
            document.getElementById("cookie-consent").style.display = "block";
            document.getElementById("accept-cookie").onclick = function() {
                document.getElementById("cookie-consent").style.display="none";
            }
        </script>';
        $html .= '</body></html>';
        return $html;
    }
}

add_action('wp_ajax_e1copy_get_page_data', 'e1copy_ai_get_page_data');
function e1copy_ai_get_page_data() {
    // Verificar se a API está válida
    e1copy_ai_check_api_access_ajax('Dados de Página');

    if (!wp_verify_nonce($_POST['nonce'], 'e1copy_get_page_data')) {
        wp_send_json_error('Nonce inválido');
    }
    $page_id = intval($_POST['page_id']);
    $page_data = get_post_meta($page_id, '_e1copy_page_data', true);
    if ($page_data) {
        $page_data['post_status'] = get_post_status($page_id);
        wp_send_json_success($page_data);
    } else {
        wp_send_json_error('Dados não encontrados');
    }
}
