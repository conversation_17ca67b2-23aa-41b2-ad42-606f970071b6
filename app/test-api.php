<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste API - E1Copy AI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>🔧 Teste de API - Validação de Chave</h3>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="apiKey" class="form-label">Chave de API:</label>
                            <input type="text" class="form-control" id="apiKey" 
                                   value="e1copy_58211d1987fae226a0e2e73dd00ebe939aa9f"
                                   placeholder="Insira sua chave de API">
                        </div>
                        
                        <div class="mb-3">
                            <button type="button" class="btn btn-primary" onclick="testValidation()">
                                🔍 Testar Validação
                            </button>
                            <button type="button" class="btn btn-success" onclick="testRegistration()">
                                📝 Testar Registro
                            </button>
                            <button type="button" class="btn btn-info" onclick="testPHP()">
                                🐘 Testar via PHP
                            </button>
                        </div>
                        
                        <div id="result" class="mt-3"></div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>📊 Logs de Debug</h5>
                    </div>
                    <div class="card-body">
                        <pre id="logs" class="bg-light p-3" style="max-height: 400px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.textContent += `[${timestamp}] ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        function showResult(success, message, data = null) {
            const result = document.getElementById('result');
            const alertClass = success ? 'alert-success' : 'alert-danger';
            const icon = success ? '✅' : '❌';
            
            let html = `
                <div class="alert ${alertClass}">
                    ${icon} <strong>${success ? 'Sucesso' : 'Erro'}:</strong> ${message}
                </div>
            `;
            
            if (data) {
                html += `
                    <div class="card">
                        <div class="card-header">📋 Dados da Resposta:</div>
                        <div class="card-body">
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    </div>
                `;
            }
            
            result.innerHTML = html;
        }
        
        function testValidation() {
            const apiKey = document.getElementById('apiKey').value.trim();
            
            if (!apiKey) {
                showResult(false, 'Por favor, insira uma chave de API');
                return;
            }
            
            log('🚀 Iniciando teste de validação...');
            log(`🔑 Chave: ${apiKey.substring(0, 15)}...`);
            
            const data = { api_key: apiKey };
            const url = '<?= url('/api/v1/validate') ?>';
            
            log(`📡 URL: ${url}`);
            log(`📦 Dados: ${JSON.stringify(data)}`);
            
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                log(`📊 Status: ${response.status} ${response.statusText}`);
                log(`📋 Headers: ${JSON.stringify([...response.headers.entries()])}`);
                return response.text();
            })
            .then(text => {
                log(`📄 Resposta bruta: ${text}`);
                
                try {
                    const data = JSON.parse(text);
                    log(`✅ JSON válido: ${JSON.stringify(data)}`);
                    
                    if (data.success) {
                        showResult(true, 'Chave validada com sucesso!', data);
                    } else {
                        showResult(false, data.message || 'Erro na validação', data);
                    }
                } catch (e) {
                    log(`❌ Erro ao parsear JSON: ${e.message}`);
                    showResult(false, 'Resposta não é JSON válido', { raw: text });
                }
            })
            .catch(error => {
                log(`❌ Erro de rede: ${error.message}`);
                showResult(false, `Erro de conexão: ${error.message}`);
            });
        }
        
        function testRegistration() {
            const apiKey = document.getElementById('apiKey').value.trim();
            
            if (!apiKey) {
                showResult(false, 'Por favor, insira uma chave de API');
                return;
            }
            
            log('🚀 Iniciando teste de registro...');
            
            const data = {
                api_key: apiKey,
                site_url: window.location.origin,
                plugin_version: '1.0.0'
            };
            
            const url = '<?= url('/api/v1/register-site') ?>';
            log(`📡 URL: ${url}`);
            log(`📦 Dados: ${JSON.stringify(data)}`);
            
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                log(`📊 Status: ${response.status}`);
                return response.json();
            })
            .then(data => {
                log(`📋 Resposta: ${JSON.stringify(data)}`);
                
                if (data.success) {
                    showResult(true, 'Site registrado com sucesso!', data);
                } else {
                    showResult(false, data.message || 'Erro no registro', data);
                }
            })
            .catch(error => {
                log(`❌ Erro: ${error.message}`);
                showResult(false, `Erro de conexão: ${error.message}`);
            });
        }
        
        function testPHP() {
            const apiKey = document.getElementById('apiKey').value.trim();
            
            if (!apiKey) {
                showResult(false, 'Por favor, insira uma chave de API');
                return;
            }
            
            log('🐘 Testando via PHP backend...');
            
            // Fazer requisição para um endpoint PHP que testa internamente
            fetch('<?= url('/test-php-validation') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `api_key=${encodeURIComponent(apiKey)}`
            })
            .then(response => response.text())
            .then(text => {
                log(`📄 Resposta PHP: ${text}`);
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        showResult(true, 'Teste PHP passou!', data);
                    } else {
                        showResult(false, data.message || 'Teste PHP falhou', data);
                    }
                } catch (e) {
                    showResult(false, 'Resposta PHP não é JSON', { raw: text });
                }
            })
            .catch(error => {
                log(`❌ Erro PHP: ${error.message}`);
                showResult(false, `Erro no teste PHP: ${error.message}`);
            });
        }
        
        // Log inicial
        log('🎯 Página de teste carregada');
        log(`🌐 Origin: ${window.location.origin}`);
    </script>
</body>
</html>

<?php
// Teste PHP direto se for requisição POST
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['api_key'])) {
    header('Content-Type: application/json');
    
    require_once __DIR__ . '/bootstrap.php';
    
    $apiKey = $_POST['api_key'];
    
    try {
        // Testar validação diretamente
        $verification = Auth::verifyApiKey($apiKey);
        
        echo json_encode([
            'success' => $verification['valid'],
            'message' => $verification['valid'] ? 'Chave válida' : $verification['reason'],
            'data' => $verification
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Erro: ' . $e->getMessage()
        ]);
    }
    exit;
}
?>
