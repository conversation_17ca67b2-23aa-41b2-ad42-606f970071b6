<?php
/**
 * Serviço de Sincronização de Sites
 * Coleta dados dos sites WordPress com plugin E1Copy AI
 */

class SiteSyncService {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Sincronizar todos os sites ativos
     */
    public function syncAllSites() {
        $sites = $this->db->fetchAll("
            SELECT cs.*, u.name as user_name, ak.api_key
            FROM client_sites cs
            LEFT JOIN users u ON cs.user_id = u.id
            LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
            WHERE cs.status IN ('connected', 'pending')
            AND u.status = 'active'
        ");
        
        $results = [];
        foreach ($sites as $site) {
            $results[] = $this->syncSite($site);
        }
        
        return $results;
    }
    
    /**
     * Sincronizar um site específico
     */
    public function syncSite($site) {
        $siteUrl = rtrim($site['site_url'], '/');
        $result = [
            'site_id' => $site['id'],
            'site_url' => $siteUrl,
            'success' => false,
            'message' => '',
            'data' => null
        ];
        
        try {
            // 1. Verificar status do plugin
            $pluginStatus = $this->checkPluginStatus($siteUrl);
            
            if (!$pluginStatus['active']) {
                $this->updateSiteStatus($site['id'], 'disconnected', 'Plugin não ativo');
                $result['message'] = 'Plugin não está ativo';
                return $result;
            }
            
            // 2. Buscar posts não processados via API REST
            $pendingPosts = $this->getPendingPostsFromAPI($siteUrl, $site['api_key']);

            if ($pendingPosts === false) {
                $this->updateSiteStatus($site['id'], 'disconnected', 'Erro ao acessar API de posts');
                $result['message'] = 'Erro ao acessar API de posts do site';
                return $result;
            }
            
            // 3. Atualizar status do site
            $this->updateSiteStatus($site['id'], 'connected', null, $pluginStatus['version']);

            // 4. Processar posts pendentes se houver
            $addedToQueue = 0;
            if (!empty($pendingPosts)) {
                $addedToQueue = $this->addPostsToQueue($site, $pendingPosts);
            }

            $result['success'] = true;
            $result['message'] = 'Sincronização concluída';
            $result['data'] = [
                'plugin_version' => $pluginStatus['version'],
                'pending_posts' => count($pendingPosts),
                'added_to_queue' => $addedToQueue
            ];
            
        } catch (Exception $e) {
            $this->updateSiteStatus($site['id'], 'disconnected', $e->getMessage());
            $result['message'] = 'Erro: ' . $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Verificar status do plugin WordPress
     */
    private function checkPluginStatus($siteUrl) {
        $url = $siteUrl . '/wp-json/e1copy/v1/status';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'E1Copy-Dashboard/1.0');
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);
            if ($data && isset($data['status'])) {
                return [
                    'active' => $data['status'] === 'active',
                    'version' => $data['version'] ?? null
                ];
            }
        }
        
        return ['active' => false, 'version' => null];
    }
    
    /**
     * Buscar posts não processados via API REST
     */
    private function getPendingPostsFromAPI($siteUrl, $apiKey = null) {
        $url = $siteUrl . '/wp-json/e1copy-ai/v1/posts';

        // Buscar chave de API REST do site se não fornecida
        if (!$apiKey) {
            $apiKey = $this->getRestApiKey($siteUrl);
        }

        $headers = [
            'Accept: application/json',
            'Content-Type: application/json',
            'User-Agent: E1Copy-Dashboard/1.0'
        ];

        // Adicionar autenticação se disponível
        if ($apiKey) {
            $headers[] = 'X-E1Copy-API-Key: ' . $apiKey;
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            error_log("SiteSyncService: Erro cURL para {$siteUrl}: {$curlError}");
            return false;
        }

        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);

            // A API retorna automaticamente apenas posts não processados (processed=0)
            if (isset($data['success']) && $data['success'] && isset($data['posts'])) {
                return $data['posts'];
            } elseif (is_array($data)) {
                return $data;
            }
        } else {
            error_log("SiteSyncService: HTTP {$httpCode} para {$siteUrl}/wp-json/e1copy-ai/v1/posts");
            if ($response) {
                error_log("SiteSyncService: Resposta: " . substr($response, 0, 500));
            }
        }

        return false;
    }

    /**
     * Obter chave de API REST do site
     */
    private function getRestApiKey($siteUrl) {
        // Tentar obter via endpoint de configuração
        $configUrl = $siteUrl . '/wp-admin/admin-ajax.php';

        $postData = [
            'action' => 'e1copy_get_rest_api_key'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $configUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'E1Copy-Dashboard/1.0');

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);
            if ($data && isset($data['success']) && $data['success'] && isset($data['api_key'])) {
                return $data['api_key'];
            }
        }

        return null;
    }
    
    /**
     * Adicionar posts à fila de processamento
     */
    private function addPostsToQueue($site, $pendingPosts) {
        $addedCount = 0;

        foreach ($pendingPosts as $post) {
            try {
                // Verificar se já existe na fila
                $existing = $this->db->fetch("
                    SELECT id FROM content_queue
                    WHERE site_id = ? AND wp_post_id = ?
                ", [$site['id'], $post['id']]);

                if (!$existing) {
                    // Extrair dados do post da API E1Copy
                    $postTitle = $post['title'] ?? 'Post sem título';
                    $postContent = $post['content'] ?? '';
                    $postExcerpt = $post['excerpt'] ?? '';
                    $keywords = $post['keywords'] ?? $post['tags'] ?? '';

                    // Se não há keywords, extrair do título e conteúdo
                    if (empty($keywords)) {
                        $keywords = $this->extractKeywords($postTitle, $postContent);
                    }

                    // Adicionar à fila de processamento
                    $this->db->query("
                        INSERT INTO content_queue (
                            user_id, site_id, site_url, wp_post_id,
                            topic, keywords, template_id,
                            status, priority, scheduled_for,
                            created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', 1, NOW(), NOW(), NOW())
                    ", [
                        $site['user_id'],
                        $site['id'],
                        $site['site_url'],
                        $post['id'],
                        $postTitle, // Usar título como tópico
                        $keywords, // Usar keywords do post ou extraídas
                        null // Template padrão será usado
                    ]);

                    $addedCount++;

                    // Log da adição à fila
                    $this->logSync($site['id'], 'success', "Post '{$postTitle}' adicionado à fila");
                }

            } catch (Exception $e) {
                $this->logSync($site['id'], 'error', "Erro ao adicionar post à fila: " . $e->getMessage());
            }
        }

        return $addedCount;
    }

    /**
     * Extrair palavras-chave do título e conteúdo
     */
    private function extractKeywords($title, $content) {
        // Remover HTML tags
        $text = strip_tags($title . ' ' . $content);

        // Converter para minúsculas e remover caracteres especiais
        $text = strtolower(preg_replace('/[^a-záàâãéèêíìîóòôõúùûç\s]/u', '', $text));

        // Dividir em palavras
        $words = array_filter(explode(' ', $text));

        // Remover palavras muito pequenas e comuns
        $stopWords = ['de', 'da', 'do', 'das', 'dos', 'e', 'o', 'a', 'os', 'as', 'em', 'no', 'na', 'nos', 'nas', 'para', 'com', 'por', 'que', 'se', 'um', 'uma', 'uns', 'umas'];
        $keywords = array_filter($words, function($word) use ($stopWords) {
            return strlen($word) > 3 && !in_array($word, $stopWords);
        });

        // Pegar as primeiras 5 palavras-chave
        return implode(', ', array_slice(array_unique($keywords), 0, 5));
    }
    
    /**
     * Atualizar status do site
     */
    private function updateSiteStatus($siteId, $status, $errorMessage = null, $pluginVersion = null) {
        $updateData = [
            'status' => $status,
            'last_connection' => date('Y-m-d H:i:s')
        ];
        
        if ($pluginVersion) {
            $updateData['plugin_version'] = $pluginVersion;
        }
        
        $setParts = [];
        $values = [];
        
        foreach ($updateData as $key => $value) {
            $setParts[] = "$key = ?";
            $values[] = $value;
        }
        
        $values[] = $siteId;
        
        $this->db->query("
            UPDATE client_sites 
            SET " . implode(', ', $setParts) . "
            WHERE id = ?
        ", $values);
        
        // Log da sincronização
        $this->logSync($siteId, $status, $errorMessage);
    }
    
    /**
     * Registrar log de sincronização
     */
    private function logSync($siteId, $status, $message = null) {
        try {
            $this->db->query("
                INSERT INTO ai_execution_logs (
                    site_id, action_type, status, 
                    error_message, created_at
                ) VALUES (?, 'site_sync', ?, ?, NOW())
            ", [$siteId, $status, $message]);
        } catch (Exception $e) {
            // Ignorar erros de log se tabela não existir
        }
    }
    
    /**
     * Sincronizar site específico por ID
     */
    public function syncSiteById($siteId) {
        $site = $this->db->fetch("
            SELECT cs.*, u.name as user_name, ak.api_key
            FROM client_sites cs
            LEFT JOIN users u ON cs.user_id = u.id
            LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
            WHERE cs.id = ?
        ", [$siteId]);
        
        if (!$site) {
            return ['success' => false, 'message' => 'Site não encontrado'];
        }
        
        return $this->syncSite($site);
    }
}
