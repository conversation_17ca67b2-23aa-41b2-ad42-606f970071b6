<?php
/**
 * Serviço de Sincronização de Sites
 * Coleta dados dos sites WordPress com plugin E1Copy AI
 */

class SiteSyncService {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Sincronizar todos os sites ativos
     */
    public function syncAllSites() {
        $sites = $this->db->fetchAll("
            SELECT cs.*, u.name as user_name, ak.api_key
            FROM client_sites cs
            LEFT JOIN users u ON cs.user_id = u.id
            LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
            WHERE cs.status IN ('connected', 'pending')
            AND u.status = 'active'
        ");
        
        $results = [];
        foreach ($sites as $site) {
            $results[] = $this->syncSite($site);
        }
        
        return $results;
    }
    
    /**
     * Sincronizar um site específico
     */
    public function syncSite($site) {
        $siteUrl = rtrim($site['site_url'], '/');
        $result = [
            'site_id' => $site['id'],
            'site_url' => $siteUrl,
            'success' => false,
            'message' => '',
            'data' => null
        ];
        
        try {
            // 1. Verificar status do plugin
            $pluginStatus = $this->checkPluginStatus($siteUrl);
            
            if (!$pluginStatus['active']) {
                $this->updateSiteStatus($site['id'], 'disconnected', 'Plugin não ativo');
                $result['message'] = 'Plugin não está ativo';
                return $result;
            }
            
            // 2. Coletar dados do site
            $siteData = $this->collectSiteData($siteUrl, $site['api_key']);
            
            if (!$siteData) {
                $this->updateSiteStatus($site['id'], 'disconnected', 'Erro ao coletar dados');
                $result['message'] = 'Erro ao coletar dados do site';
                return $result;
            }
            
            // 3. Verificar posts pendentes no WordPress
            $pendingPosts = $this->checkPendingPosts($siteData);
            
            // 4. Atualizar status do site
            $this->updateSiteStatus($site['id'], 'connected', null, $pluginStatus['version']);
            
            // 5. Processar posts pendentes se houver
            if (!empty($pendingPosts)) {
                $this->processPendingPosts($site, $pendingPosts);
            }
            
            $result['success'] = true;
            $result['message'] = 'Sincronização concluída';
            $result['data'] = [
                'plugin_version' => $pluginStatus['version'],
                'pending_posts' => count($pendingPosts),
                'total_posts' => $siteData['stats']['total_posts'] ?? 0
            ];
            
        } catch (Exception $e) {
            $this->updateSiteStatus($site['id'], 'disconnected', $e->getMessage());
            $result['message'] = 'Erro: ' . $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Verificar status do plugin WordPress
     */
    private function checkPluginStatus($siteUrl) {
        $url = $siteUrl . '/wp-json/e1copy/v1/status';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'E1Copy-Dashboard/1.0');
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);
            if ($data && isset($data['status'])) {
                return [
                    'active' => $data['status'] === 'active',
                    'version' => $data['version'] ?? null
                ];
            }
        }
        
        return ['active' => false, 'version' => null];
    }
    
    /**
     * Coletar dados do site via AJAX
     */
    private function collectSiteData($siteUrl, $apiKey) {
        $url = $siteUrl . '/wp-admin/admin-ajax.php';
        
        $postData = [
            'action' => 'e1copy_get_site_data',
            'api_key' => $apiKey
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'E1Copy-Dashboard/1.0');
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);
            if ($data && isset($data['success']) && $data['success']) {
                return $data['data'];
            }
        }
        
        return null;
    }
    
    /**
     * Verificar posts pendentes no WordPress
     */
    private function checkPendingPosts($siteData) {
        $pendingPosts = [];
        
        // Verificar se há posts com status "pending" ou "draft" que podem ser do E1Copy
        if (isset($siteData['recent_posts'])) {
            foreach ($siteData['recent_posts'] as $post) {
                // Aqui você pode implementar lógica para identificar posts pendentes
                // Por exemplo, posts com meta específica ou padrões de título
                if (strpos($post['title'], '[E1Copy]') !== false || 
                    strpos($post['title'], 'Pendente') !== false) {
                    $pendingPosts[] = $post;
                }
            }
        }
        
        return $pendingPosts;
    }
    
    /**
     * Processar posts pendentes encontrados
     */
    private function processPendingPosts($site, $pendingPosts) {
        foreach ($pendingPosts as $post) {
            // Verificar se já existe na nossa base
            $existing = $this->db->fetch("
                SELECT id FROM generated_posts 
                WHERE site_id = ? AND wp_post_id = ?
            ", [$site['id'], $post['id']]);
            
            if (!$existing) {
                // Adicionar à nossa base de dados
                $this->db->query("
                    INSERT INTO generated_posts (
                        user_id, site_id, site_url, wp_post_id,
                        post_title, post_content, post_excerpt,
                        generation_status, publish_status,
                        created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', 'draft', NOW(), NOW())
                ", [
                    $site['user_id'],
                    $site['id'],
                    $site['site_url'],
                    $post['id'],
                    $post['title'],
                    $post['excerpt'] ?? '',
                    $post['excerpt'] ?? '',
                ]);
            }
        }
    }
    
    /**
     * Atualizar status do site
     */
    private function updateSiteStatus($siteId, $status, $errorMessage = null, $pluginVersion = null) {
        $updateData = [
            'status' => $status,
            'last_connection' => date('Y-m-d H:i:s')
        ];
        
        if ($pluginVersion) {
            $updateData['plugin_version'] = $pluginVersion;
        }
        
        $setParts = [];
        $values = [];
        
        foreach ($updateData as $key => $value) {
            $setParts[] = "$key = ?";
            $values[] = $value;
        }
        
        $values[] = $siteId;
        
        $this->db->query("
            UPDATE client_sites 
            SET " . implode(', ', $setParts) . "
            WHERE id = ?
        ", $values);
        
        // Log da sincronização
        $this->logSync($siteId, $status, $errorMessage);
    }
    
    /**
     * Registrar log de sincronização
     */
    private function logSync($siteId, $status, $message = null) {
        try {
            $this->db->query("
                INSERT INTO ai_execution_logs (
                    site_id, action_type, status, 
                    error_message, created_at
                ) VALUES (?, 'site_sync', ?, ?, NOW())
            ", [$siteId, $status, $message]);
        } catch (Exception $e) {
            // Ignorar erros de log se tabela não existir
        }
    }
    
    /**
     * Sincronizar site específico por ID
     */
    public function syncSiteById($siteId) {
        $site = $this->db->fetch("
            SELECT cs.*, u.name as user_name, ak.api_key
            FROM client_sites cs
            LEFT JOIN users u ON cs.user_id = u.id
            LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
            WHERE cs.id = ?
        ", [$siteId]);
        
        if (!$site) {
            return ['success' => false, 'message' => 'Site não encontrado'];
        }
        
        return $this->syncSite($site);
    }
}
