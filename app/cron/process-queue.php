<?php
/**
 * Script de Processamento da Fila de Conteúdo
 * Execute via cron: */5 * * * * php /path/to/app/cron/process-queue.php
 */

// Incluir bootstrap
require_once __DIR__ . '/../bootstrap.php';

// Configurações
$maxItems = 5; // Máximo de itens a processar por execução
$lockFile = __DIR__ . '/queue-process.lock';
$logFile = __DIR__ . '/queue-process.log';

// Função de log
function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[{$timestamp}] {$message}\n", FILE_APPEND | LOCK_EX);
}

// Verificar se já está rodando
if (file_exists($lockFile)) {
    $lockTime = filemtime($lockFile);
    $currentTime = time();
    
    // Se o lock tem mais de 10 minutos, considerar travado e remover
    if (($currentTime - $lockTime) > 600) {
        unlink($lockFile);
        logMessage("Lock antigo removido (mais de 10 minutos)");
    } else {
        logMessage("Processamento já em andamento, saindo...");
        exit(0);
    }
}

// Criar lock
file_put_contents($lockFile, getmypid());

try {
    logMessage("Iniciando processamento da fila...");
    
    // Verificar se o processamento está habilitado
    $db = Database::getInstance();
    $setting = $db->fetch("SELECT setting_value FROM ai_settings WHERE setting_key = 'queue_processing_enabled'");
    
    if (!$setting || $setting['setting_value'] !== 'true') {
        logMessage("Processamento da fila está desabilitado");
        exit(0);
    }
    
    // Inicializar serviço
    $queueService = new ContentQueueService();
    
    // Processar fila
    $result = $queueService->processAll($maxItems);
    
    logMessage("Processamento concluído: {$result['processed']} itens processados");
    
    // Log detalhado dos resultados
    foreach ($result['results'] as $index => $itemResult) {
        $status = $itemResult['success'] ? 'SUCESSO' : 'ERRO';
        $message = "Item " . ($index + 1) . ": {$status}";
        
        if (!$itemResult['success']) {
            $message .= " - " . $itemResult['error'];
        }
        
        if (isset($itemResult['execution_time'])) {
            $message .= " (tempo: " . round($itemResult['execution_time']) . "ms)";
        }
        
        logMessage($message);
    }
    
    // Limpeza de itens antigos (uma vez por dia)
    $lastCleanup = get_option('last_queue_cleanup', 0);
    if ((time() - $lastCleanup) > 86400) { // 24 horas
        $deleted = $queueService->cleanupOldItems(30);
        logMessage("Limpeza executada: {$deleted} itens antigos removidos");
        update_option('last_queue_cleanup', time());
    }
    
} catch (Exception $e) {
    logMessage("ERRO: " . $e->getMessage());
    logMessage("Stack trace: " . $e->getTraceAsString());
} finally {
    // Remover lock
    if (file_exists($lockFile)) {
        unlink($lockFile);
    }
    
    logMessage("Processamento finalizado\n");
}

// Funções auxiliares para opções
function get_option($key, $default = null) {
    global $db;
    if (!isset($db)) {
        $db = Database::getInstance();
    }
    
    $result = $db->fetch("SELECT setting_value FROM ai_settings WHERE setting_key = :key", ['key' => $key]);
    return $result ? $result['setting_value'] : $default;
}

function update_option($key, $value) {
    global $db;
    if (!isset($db)) {
        $db = Database::getInstance();
    }
    
    $exists = $db->fetch("SELECT id FROM ai_settings WHERE setting_key = :key", ['key' => $key]);
    
    if ($exists) {
        $db->update('ai_settings', ['setting_value' => $value], 'setting_key = :key', ['key' => $key]);
    } else {
        $db->insert('ai_settings', [
            'setting_key' => $key,
            'setting_value' => $value,
            'setting_type' => 'string'
        ]);
    }
}
