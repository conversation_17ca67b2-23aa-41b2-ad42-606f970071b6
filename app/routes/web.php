<?php
/**
 * Rotas Web da Aplicação
 * Sistema de Dashboard E1Copy AI
 */

// Página inicial - redirecionar para login
$router->get('/', function() {
    if (Auth::check()) {
        if (Auth::isAdmin()) {
            redirect(url('/admin/dashboard'));
        } else {
            redirect(url('/client/dashboard'));
        }
    } else {
        redirect(url('/login'));
    }
});

// Página de teste CSS
$router->get('/test-css', function() {
    include __DIR__ . '/../test-css.php';
});

// Página de teste de validação
$router->get('/test-validation', function() {
    include __DIR__ . '/../test-validation.php';
});

// Página de teste de API
$router->get('/test-api', function() {
    include __DIR__ . '/../test-api.php';
});

// Teste PHP de validação
$router->post('/test-php-validation', function() {
    include __DIR__ . '/../test-api.php';
});

// Verificação de chave específica
$router->get('/check-key', function() {
    header('Content-Type: text/plain');
    include __DIR__ . '/../check-key.php';
});

// Rotas de autenticação
$router->get('/login', 'Auth@showLogin');
$router->post('/login', 'Auth@login');
$router->get('/logout', 'Auth@logout');

// Rotas do cliente
$router->get('/client/dashboard', 'Client@dashboard', ['AuthMiddleware']);
$router->get('/client/account', 'Client@account', ['AuthMiddleware']);
$router->post('/client/account', 'Client@updateAccount', ['AuthMiddleware']);
$router->get('/client/sites', 'Client@sites', ['AuthMiddleware']);
$router->post('/client/sites/add', 'Client@addSite', ['AuthMiddleware']);
$router->post('/client/sites/remove/{id}', 'Client@removeSite', ['AuthMiddleware']);
$router->post('/client/sites/check/{id}', 'Client@checkSiteConnection', ['AuthMiddleware']);
$router->get('/client/sites/api-key/{id}', 'Client@getSiteApiKey', ['AuthMiddleware']);
$router->post('/client/sites/regenerate-api-key/{id}', 'Client@regenerateSiteApiKey', ['AuthMiddleware']);
$router->post('/client/generate-api-key', 'Client@generateApiKey', ['AuthMiddleware']);
$router->post('/client/revoke-api-key/{id}', 'Client@revokeApiKey', ['AuthMiddleware']);

// Rotas do admin
$router->get('/admin/login', 'Admin@showLogin');
$router->post('/admin/login', 'Admin@login');
$router->get('/admin/dashboard', 'Admin@dashboard', ['AdminMiddleware']);
$router->get('/admin/clients', 'Admin@clients', ['AdminMiddleware']);
$router->post('/admin/clients/create', 'Admin@createClient', ['AdminMiddleware']);
$router->get('/admin/clients/{id}', 'Admin@showClient', ['AdminMiddleware']);
$router->post('/admin/clients/{id}', 'Admin@updateClient', ['AdminMiddleware']);
$router->get('/admin/plans', 'Admin@plans', ['AdminMiddleware']);
$router->post('/admin/plans/create', 'Admin@createPlan', ['AdminMiddleware']);
$router->post('/admin/plans/{id}', 'Admin@updatePlan', ['AdminMiddleware']);
$router->get('/admin/settings', 'Admin@settings', ['AdminMiddleware']);
$router->post('/admin/settings', 'Admin@updateSettings', ['AdminMiddleware']);
$router->get('/admin/account', 'Admin@account', ['AdminMiddleware']);
$router->post('/admin/account', 'Admin@updateAccount', ['AdminMiddleware']);
$router->get('/admin/key-diagnostic', function() {
    include __DIR__ . '/../admin/key-diagnostic.php';
}, ['AdminMiddleware']);

// Rotas de Relatórios
$router->get('/admin/reports/posts-clients', 'ReportsController@postsClients', ['AdminMiddleware']);
$router->get('/admin/reports/ai-usage', 'ReportsController@aiUsage', ['AdminMiddleware']);

// Rotas de Configurações de IA
$router->get('/admin/ai-settings', 'AiSettingsController@index', ['AdminMiddleware']);
$router->post('/admin/ai-settings', 'AiSettingsController@update', ['AdminMiddleware']);
$router->get('/admin/ai-settings/templates', 'AiSettingsController@templates', ['AdminMiddleware']);
$router->post('/admin/ai-settings/templates', 'AiSettingsController@saveTemplate', ['AdminMiddleware']);
$router->post('/admin/ai-settings/templates/{id}', 'AiSettingsController@updateTemplate', ['AdminMiddleware']);
$router->delete('/admin/ai-settings/templates/{id}', 'AiSettingsController@deleteTemplate', ['AdminMiddleware']);

// Rotas de Geração de Conteúdo
$router->post('/api/content/add-to-queue', 'ContentController@addToQueue', ['AuthMiddleware']);
$router->post('/api/content/generate-now', 'ContentController@generateNow', ['AuthMiddleware']);
$router->post('/api/content/publish', 'ContentController@publishPost', ['AuthMiddleware']);
$router->get('/api/content/posts', 'ContentController@listPosts', ['AuthMiddleware']);
$router->post('/api/content/process-queue', 'ContentController@processQueue', ['AdminMiddleware']);
$router->get('/api/content/queue-stats', 'ContentController@getQueueStats', ['AdminMiddleware']);
$router->post('/api/content/cancel-queue-item', 'ContentController@cancelQueueItem', ['AuthMiddleware']);
$router->post('/api/content/retry-queue-item', 'ContentController@retryQueueItem', ['AuthMiddleware']);
