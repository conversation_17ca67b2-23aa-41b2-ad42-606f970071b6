<?php
/**
 * <PERSON><PERSON>s Web da Aplicação
 * Sistema de Dashboard E1Copy AI
 */

// Página inicial - redirecionar para login
$router->get('/', function() {
    if (Auth::check()) {
        if (Auth::isAdmin()) {
            redirect(url('/admin/dashboard'));
        } else {
            redirect(url('/client/dashboard'));
        }
    } else {
        redirect(url('/login'));
    }
});

// Rotas de autenticação
$router->get('/login', 'Auth@showLogin');
$router->post('/login', 'Auth@login');
$router->get('/logout', 'Auth@logout');

// Rotas do cliente
$router->get('/client/dashboard', 'Client@dashboard', ['AuthMiddleware']);
$router->get('/client/account', 'Client@account', ['AuthMiddleware']);
$router->post('/client/account', 'Client@updateAccount', ['AuthMiddleware']);
$router->post('/client/generate-api-key', 'Client@generateApiKey', ['AuthMiddleware']);
$router->post('/client/revoke-api-key/{id}', 'Client@revokeApiKey', ['AuthMiddleware']);

// Rotas do admin
$router->get('/admin/login', 'Admin@showLogin');
$router->post('/admin/login', 'Admin@login');
$router->get('/admin/dashboard', 'Admin@dashboard', ['AdminMiddleware']);
$router->get('/admin/clients', 'Admin@clients', ['AdminMiddleware']);
$router->get('/admin/clients/{id}', 'Admin@showClient', ['AdminMiddleware']);
$router->post('/admin/clients/{id}', 'Admin@updateClient', ['AdminMiddleware']);
$router->get('/admin/settings', 'Admin@settings', ['AdminMiddleware']);
$router->post('/admin/settings', 'Admin@updateSettings', ['AdminMiddleware']);
$router->get('/admin/account', 'Admin@account', ['AdminMiddleware']);
$router->post('/admin/account', 'Admin@updateAccount', ['AdminMiddleware']);
