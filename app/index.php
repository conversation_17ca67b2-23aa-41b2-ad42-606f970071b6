<?php
/**
 * Ponto de Entrada da Aplicação
 * Sistema de Dashboard E1Copy AI
 */

// Carregar bootstrap
require_once __DIR__ . '/bootstrap.php';

// Inicializar roteador
$router = new Router();

// Carregar rotas
require_once __DIR__ . '/routes/web.php';
require_once __DIR__ . '/routes/api.php';

// Executar roteamento
try {
    $router->dispatch();
} catch (Exception $e) {
    if (config('app.debug')) {
        echo '<h1>Erro:</h1>';
        echo '<p>' . $e->getMessage() . '</p>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    } else {
        http_response_code(500);
        echo 'Erro interno do servidor';
    }
}
