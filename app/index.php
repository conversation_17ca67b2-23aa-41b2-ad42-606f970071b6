<?php
/**
 * Ponto de Entrada da Aplicação
 * Sistema de Dashboard E1Copy AI
 */

// Carregar bootstrap
require_once __DIR__ . '/bootstrap.php';

// Carregar rotas
require_once __DIR__ . '/routes/web.php';
require_once __DIR__ . '/routes/api.php';

// Inicializar roteador
$router = new Router();

// Definir rotas web
$router->get('/', 'Home@index');
$router->get('/login', 'Auth@showLogin');
$router->post('/login', 'Auth@login');
$router->get('/logout', 'Auth@logout');

// Rotas do cliente
$router->get('/client/dashboard', 'Client@dashboard', ['AuthMiddleware']);
$router->get('/client/account', 'Client@account', ['AuthMiddleware']);
$router->post('/client/account', 'Client@updateAccount', ['AuthMiddleware']);

// Rotas do admin
$router->get('/admin/login', 'Admin@showLogin');
$router->post('/admin/login', 'Admin@login');
$router->get('/admin/dashboard', 'Admin@dashboard', ['AdminMiddleware']);
$router->get('/admin/clients', 'Admin@clients', ['AdminMiddleware']);
$router->get('/admin/clients/{id}', 'Admin@showClient', ['AdminMiddleware']);
$router->post('/admin/clients/{id}', 'Admin@updateClient', ['AdminMiddleware']);
$router->get('/admin/settings', 'Admin@settings', ['AdminMiddleware']);
$router->post('/admin/settings', 'Admin@updateSettings', ['AdminMiddleware']);
$router->get('/admin/account', 'Admin@account', ['AdminMiddleware']);

// Rotas da API
$router->get('/api/v1/verify-key', 'Api@verifyKey');
$router->post('/api/v1/verify-key', 'Api@verifyKey');
$router->get('/api/v1/client-status/{key}', 'Api@clientStatus');
$router->post('/api/v1/suspend-key', 'Api@suspendKey');
$router->post('/api/v1/activate-key', 'Api@activateKey');

// Executar roteamento
try {
    $router->dispatch();
} catch (Exception $e) {
    if (config('app.debug')) {
        echo '<h1>Erro:</h1>';
        echo '<p>' . $e->getMessage() . '</p>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    } else {
        http_response_code(500);
        echo 'Erro interno do servidor';
    }
}
