<?php
/**
 * Controller de Ferramentas Administrativas
 * Ferramentas para debug e teste do sistema
 */

class ToolsController extends Controller {
    
    public function index() {
        $this->view('admin.tools.index', [
            'title' => 'Ferramentas de Debug'
        ]);
    }
    
    /**
     * Testar conexão com plugin WordPress
     */
    public function testPluginConnection() {
        try {
            $siteUrl = $_POST['site_url'] ?? '';
            $apiKey = $_POST['api_key'] ?? '';
            
            if (empty($siteUrl) || empty($apiKey)) {
                $this->json([
                    'success' => false,
                    'message' => 'URL do site e chave de API são obrigatórios'
                ]);
                return;
            }
            
            $siteUrl = rtrim($siteUrl, '/');
            $results = [];
            
            // Teste 1: Endpoint de teste (sem autenticação)
            $results['test_endpoint'] = $this->testEndpoint($siteUrl . '/wp-json/e1copy-ai/v1/test');
            
            // Teste 2: Verificação de chave
            $results['verify_key'] = $this->testEndpoint(
                $siteUrl . '/wp-json/e1copy-ai/v1/verify-key',
                ['X-E1Copy-API-Key: ' . $apiKey]
            );
            
            // Teste 3: Listar posts
            $results['list_posts'] = $this->testEndpoint(
                $siteUrl . '/wp-json/e1copy-ai/v1/posts',
                ['X-E1Copy-API-Key: ' . $apiKey]
            );
            
            // Teste 4: Status do plugin
            $results['plugin_status'] = $this->testEndpoint($siteUrl . '/wp-json/e1copy/v1/status');
            
            $this->json([
                'success' => true,
                'message' => 'Testes de conexão concluídos',
                'data' => $results
            ]);
            
        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Erro nos testes: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Validar chave de API
     */
    public function validateApiKey() {
        try {
            $apiKey = $_POST['api_key'] ?? '';
            
            if (empty($apiKey)) {
                $this->json([
                    'success' => false,
                    'message' => 'Chave de API é obrigatória'
                ]);
                return;
            }
            
            $db = Database::getInstance();
            
            // Verificar se a chave existe no sistema
            $keyInfo = $db->fetch("
                SELECT ak.*, cs.site_url, u.name as user_name
                FROM api_keys ak
                LEFT JOIN client_sites cs ON ak.id = cs.api_key_id
                LEFT JOIN users u ON ak.user_id = u.id
                WHERE ak.api_key = ?
            ", [$apiKey]);
            
            if (!$keyInfo) {
                $this->json([
                    'success' => false,
                    'message' => 'Chave não encontrada no sistema',
                    'data' => [
                        'key_prefix' => substr($apiKey, 0, 8) . '...',
                        'exists_in_db' => false
                    ]
                ]);
                return;
            }
            
            $this->json([
                'success' => true,
                'message' => 'Chave válida encontrada no sistema',
                'data' => [
                    'key_prefix' => substr($apiKey, 0, 8) . '...',
                    'exists_in_db' => true,
                    'user_name' => $keyInfo['user_name'],
                    'site_url' => $keyInfo['site_url'],
                    'status' => $keyInfo['status'],
                    'created_at' => $keyInfo['created_at']
                ]
            ]);
            
        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Erro na validação: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Debug completo de um site
     */
    public function debugSite() {
        try {
            $siteId = $_POST['site_id'] ?? '';
            
            if (empty($siteId)) {
                $this->json([
                    'success' => false,
                    'message' => 'ID do site é obrigatório'
                ]);
                return;
            }
            
            $db = Database::getInstance();
            
            // Buscar informações do site
            $site = $db->fetch("
                SELECT cs.*, u.name as user_name, u.email, ak.api_key, ak.status as key_status
                FROM client_sites cs
                LEFT JOIN users u ON cs.user_id = u.id
                LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
                WHERE cs.id = ?
            ", [$siteId]);
            
            if (!$site) {
                $this->json([
                    'success' => false,
                    'message' => 'Site não encontrado'
                ]);
                return;
            }
            
            // Testar conexão
            $syncService = new SiteSyncService();
            $syncResult = $syncService->syncSiteById($siteId);
            
            // Buscar logs recentes
            $logs = $db->fetchAll("
                SELECT * FROM ai_execution_logs 
                WHERE site_id = ? 
                ORDER BY created_at DESC 
                LIMIT 10
            ", [$siteId]);
            
            $this->json([
                'success' => true,
                'message' => 'Debug do site concluído',
                'data' => [
                    'site_info' => $site,
                    'sync_result' => $syncResult,
                    'recent_logs' => $logs
                ]
            ]);
            
        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Erro no debug: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Testar todos os endpoints de um site
     */
    public function testEndpoints() {
        try {
            $siteUrl = $_POST['site_url'] ?? '';
            
            if (empty($siteUrl)) {
                $this->json([
                    'success' => false,
                    'message' => 'URL do site é obrigatória'
                ]);
                return;
            }
            
            $siteUrl = rtrim($siteUrl, '/');
            $endpoints = [
                'WordPress REST API' => $siteUrl . '/wp-json/',
                'E1Copy Status' => $siteUrl . '/wp-json/e1copy/v1/status',
                'E1Copy AI Test' => $siteUrl . '/wp-json/e1copy-ai/v1/test',
                'E1Copy AI Posts' => $siteUrl . '/wp-json/e1copy-ai/v1/posts',
                'WordPress Health' => $siteUrl . '/wp-json/wp/v2/posts?per_page=1'
            ];
            
            $results = [];
            foreach ($endpoints as $name => $url) {
                $results[$name] = $this->testEndpoint($url);
            }
            
            $this->json([
                'success' => true,
                'message' => 'Teste de endpoints concluído',
                'data' => $results
            ]);
            
        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Erro nos testes: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Verificar integridade do banco de dados
     */
    public function checkDatabase() {
        try {
            $db = Database::getInstance();
            $results = [];
            
            // Verificar tabelas principais
            $tables = ['users', 'client_sites', 'api_keys', 'content_queue', 'ai_execution_logs'];
            
            foreach ($tables as $table) {
                try {
                    $count = $db->fetch("SELECT COUNT(*) as count FROM $table")['count'];
                    $results['tables'][$table] = [
                        'exists' => true,
                        'count' => $count,
                        'status' => 'OK'
                    ];
                } catch (Exception $e) {
                    $results['tables'][$table] = [
                        'exists' => false,
                        'error' => $e->getMessage(),
                        'status' => 'ERROR'
                    ];
                }
            }
            
            // Verificar integridade dos dados
            $results['integrity'] = [
                'sites_without_keys' => $db->fetch("
                    SELECT COUNT(*) as count 
                    FROM client_sites cs 
                    LEFT JOIN api_keys ak ON cs.api_key_id = ak.id 
                    WHERE ak.id IS NULL
                ")['count'],
                'keys_without_users' => $db->fetch("
                    SELECT COUNT(*) as count 
                    FROM api_keys ak 
                    LEFT JOIN users u ON ak.user_id = u.id 
                    WHERE u.id IS NULL
                ")['count']
            ];
            
            $this->json([
                'success' => true,
                'message' => 'Verificação do banco concluída',
                'data' => $results
            ]);
            
        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Erro na verificação: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Informações do sistema
     */
    public function systemInfo() {
        try {
            $db = Database::getInstance();
            
            $info = [
                'php_version' => PHP_VERSION,
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'N/A',
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
                'curl_enabled' => function_exists('curl_init'),
                'database' => [
                    'connected' => true,
                    'total_users' => $db->fetch("SELECT COUNT(*) as count FROM users")['count'],
                    'total_sites' => $db->fetch("SELECT COUNT(*) as count FROM client_sites")['count'],
                    'total_keys' => $db->fetch("SELECT COUNT(*) as count FROM api_keys")['count']
                ],
                'disk_space' => [
                    'free' => disk_free_space('.'),
                    'total' => disk_total_space('.')
                ]
            ];
            
            $this->json([
                'success' => true,
                'message' => 'Informações do sistema coletadas',
                'data' => $info
            ]);
            
        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Erro ao coletar informações: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Testar um endpoint específico
     */
    private function testEndpoint($url, $headers = []) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'E1Copy-Dashboard-Tools/1.0');
        
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge([
                'Accept: application/json',
                'Content-Type: application/json'
            ], $headers));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        return [
            'url' => $url,
            'http_code' => $httpCode,
            'success' => $httpCode >= 200 && $httpCode < 300,
            'response' => $response ? json_decode($response, true) : null,
            'error' => $error ?: null,
            'response_size' => strlen($response ?: '')
        ];
    }
}
