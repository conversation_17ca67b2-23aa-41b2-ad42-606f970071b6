<?php
/**
 * Controller da Área Administrativa
 * Sistema de Dashboard E1Copy AI
 */

class AdminController extends Controller {
    
    public function showLogin() {
        // Se já estiver logado como admin, redirecionar
        if (Auth::check() && Auth::isAdmin()) {
            redirect(url('/admin/dashboard'));
        }
        
        $this->view('admin.login');
    }
    
    public function login() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            $this->flash('error', 'Token de segurança inválido');
            return $this->withInput()->redirect(url('/admin/login'));
        }
        
        // Validar dados
        $validation = $this->validate([
            'email' => 'required|email',
            'password' => 'required|min:6'
        ]);
        
        if ($validation !== true) {
            return $this->withErrors($validation)->withInput()->redirect(url('/admin/login'));
        }
        
        $email = $this->input('email');
        $password = $this->input('password');
        
        if (Auth::attempt($email, $password)) {
            // Verificar se é admin
            if (!Auth::isAdmin()) {
                Auth::logout();
                $this->flash('error', 'Acesso negado. Apenas administradores podem acessar esta área.');
                return redirect(url('/admin/login'));
            }
            
            // Login bem-sucedido
            $intendedUrl = $_SESSION['intended_url'] ?? null;
            unset($_SESSION['intended_url']);
            
            redirect($intendedUrl ?: url('/admin/dashboard'));
        } else {
            $this->flash('error', 'Email ou senha incorretos');
            return $this->withInput()->redirect(url('/admin/login'));
        }
    }
    
    public function dashboard() {
        // Estatísticas gerais
        $stats = [
            'total_users' => $this->db->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'client'")['count'],
            'active_subscriptions' => $this->db->fetch("SELECT COUNT(*) as count FROM subscriptions WHERE status = 'active'")['count'],
            'total_api_keys' => $this->db->fetch("SELECT COUNT(*) as count FROM api_keys WHERE status = 'active'")['count'],
            'today_requests' => $this->db->fetch("SELECT COUNT(*) as count FROM api_usage WHERE DATE(created_at) = CURDATE()")['count']
        ];
        
        // Usuários recentes
        $recentUsers = $this->db->fetchAll(
            "SELECT * FROM users WHERE role = 'client' ORDER BY created_at DESC LIMIT 5"
        );
        
        // Atividade recente da API
        $recentActivity = $this->db->fetchAll(
            "SELECT au.*, u.name, u.email 
             FROM api_usage au
             JOIN users u ON au.user_id = u.id
             ORDER BY au.created_at DESC 
             LIMIT 10"
        );
        
        $this->view('admin.dashboard', [
            'stats' => $stats,
            'recentUsers' => $recentUsers,
            'recentActivity' => $recentActivity
        ]);
    }
    
    public function clients() {
        $page = (int) $this->input('page', 1);
        $search = $this->input('search', '');
        $status = $this->input('status', '');
        
        $conditions = "role = 'client'";
        $params = [];
        
        if ($search) {
            $conditions .= " AND (name LIKE :search OR email LIKE :search)";
            $params['search'] = "%{$search}%";
        }
        
        if ($status) {
            $conditions .= " AND status = :status";
            $params['status'] = $status;
        }
        
        $clients = $this->db->paginate($page, 20, $conditions, $params);
        
        // Buscar informações de assinatura para cada cliente
        foreach ($clients['data'] as &$client) {
            $subscription = $this->db->fetch(
                "SELECT s.*, p.name as plan_name 
                 FROM subscriptions s
                 JOIN plans p ON s.plan_id = p.id
                 WHERE s.user_id = :user_id AND s.status = 'active'
                 ORDER BY s.created_at DESC
                 LIMIT 1",
                ['user_id' => $client['id']]
            );
            $client['subscription'] = $subscription;
        }
        
        $this->view('admin.clients', [
            'clients' => $clients,
            'search' => $search,
            'status' => $status
        ]);
    }
    
    public function showClient($id) {
        $client = $this->db->fetch("SELECT * FROM users WHERE id = :id AND role = 'client'", ['id' => $id]);
        
        if (!$client) {
            $this->flash('error', 'Cliente não encontrado');
            return redirect(url('/admin/clients'));
        }
        
        // Buscar assinaturas
        $subscriptions = $this->db->fetchAll(
            "SELECT s.*, p.name as plan_name 
             FROM subscriptions s
             JOIN plans p ON s.plan_id = p.id
             WHERE s.user_id = :user_id
             ORDER BY s.created_at DESC",
            ['user_id' => $id]
        );
        
        // Buscar chaves de API
        $apiKeys = $this->db->fetchAll(
            "SELECT * FROM api_keys WHERE user_id = :user_id ORDER BY created_at DESC",
            ['user_id' => $id]
        );
        
        // Estatísticas de uso
        $usageStats = $this->db->fetch(
            "SELECT 
                COUNT(*) as total_requests,
                COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_requests,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as month_requests
             FROM api_usage 
             WHERE user_id = :user_id",
            ['user_id' => $id]
        );
        
        $this->view('admin.client-detail', [
            'client' => $client,
            'subscriptions' => $subscriptions,
            'apiKeys' => $apiKeys,
            'usageStats' => $usageStats
        ]);
    }
    
    public function updateClient($id) {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            $this->flash('error', 'Token de segurança inválido');
            return redirect(url("/admin/clients/{$id}"));
        }
        
        $client = $this->db->fetch("SELECT * FROM users WHERE id = :id AND role = 'client'", ['id' => $id]);
        
        if (!$client) {
            $this->flash('error', 'Cliente não encontrado');
            return redirect(url('/admin/clients'));
        }
        
        $action = $this->input('action');
        
        switch ($action) {
            case 'update_status':
                $status = $this->input('status');
                if (in_array($status, ['active', 'inactive', 'suspended'])) {
                    $this->db->update('users', ['status' => $status], 'id = :id', ['id' => $id]);
                    $this->flash('success', 'Status do cliente atualizado com sucesso!');
                } else {
                    $this->flash('error', 'Status inválido');
                }
                break;
                
            case 'suspend_keys':
                $this->db->query(
                    "UPDATE api_keys SET status = 'suspended' WHERE user_id = :user_id AND status = 'active'",
                    ['user_id' => $id]
                );
                $this->flash('success', 'Todas as chaves do cliente foram suspensas!');
                break;
                
            case 'activate_keys':
                $this->db->query(
                    "UPDATE api_keys SET status = 'active' WHERE user_id = :user_id AND status = 'suspended'",
                    ['user_id' => $id]
                );
                $this->flash('success', 'Todas as chaves do cliente foram reativadas!');
                break;
                
            default:
                $this->flash('error', 'Ação inválida');
        }
        
        return redirect(url("/admin/clients/{$id}"));
    }
    
    public function settings() {
        // Buscar todas as configurações
        $settings = $this->db->fetchAll("SELECT * FROM settings ORDER BY key_name");
        
        // Organizar por categoria
        $settingsGrouped = [];
        foreach ($settings as $setting) {
            $category = explode('_', $setting['key_name'])[0];
            $settingsGrouped[$category][] = $setting;
        }
        
        $this->view('admin.settings', [
            'settingsGrouped' => $settingsGrouped
        ]);
    }
    
    public function updateSettings() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            $this->flash('error', 'Token de segurança inválido');
            return redirect(url('/admin/settings'));
        }
        
        $settings = $this->input('settings', []);
        
        foreach ($settings as $key => $value) {
            $this->db->update('settings', ['value' => $value], 'key_name = :key', ['key' => $key]);
        }
        
        $this->flash('success', 'Configurações atualizadas com sucesso!');
        return redirect(url('/admin/settings'));
    }
    
    public function account() {
        $user = Auth::user();
        
        $this->view('admin.account', [
            'user' => $user
        ]);
    }
}
