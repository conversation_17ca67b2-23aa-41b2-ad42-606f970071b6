<?php
$title = 'Dashboard - E1Copy AI';
$pageTitle = 'Dashboard do Cliente';
ob_start();
?>

<div class="row">
    <!-- Estatísticas -->
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= $stats['total_requests'] ?? 0 ?></h4>
                        <p class="card-text">Total de Requisições</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= $stats['today_requests'] ?? 0 ?></h4>
                        <p class="card-text">Hoje</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-day fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= $stats['month_requests'] ?? 0 ?></h4>
                        <p class="card-text">Este Mês</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= count($apiKeys) ?></h4>
                        <p class="card-text">Chaves de API</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-key fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Informações da Assinatura -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    Minha Assinatura
                </h5>
            </div>
            <div class="card-body">
                <?php if ($subscription): ?>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Plano:</strong><br>
                            <span class="badge bg-primary fs-6"><?= e($subscription['plan_name']) ?></span>
                        </div>
                        <div class="col-sm-6">
                            <strong>Status:</strong><br>
                            <span class="badge bg-<?= $subscription['status'] === 'active' ? 'success' : 'danger' ?> fs-6">
                                <?= ucfirst($subscription['status']) ?>
                            </span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Limite Mensal:</strong><br>
                            <?= $subscription['limits_per_month'] ? number_format($subscription['limits_per_month']) . ' requisições' : 'Ilimitado' ?>
                        </div>
                        <div class="col-sm-6">
                            <strong>Sites Permitidos:</strong><br>
                            <?= $subscription['max_sites'] ?? 'Ilimitado' ?>
                        </div>
                    </div>
                    
                    <?php if ($subscription['ends_at']): ?>
                        <hr>
                        <div class="row">
                            <div class="col-12">
                                <strong>Próxima Cobrança:</strong><br>
                                <?= date('d/m/Y', strtotime($subscription['next_billing_date'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5>Nenhuma assinatura ativa</h5>
                        <p class="text-muted">Entre em contato com o suporte para ativar sua assinatura.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Chaves de API -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-key me-2"></i>
                    Chaves de API
                </h5>
                <?php if ($subscription && $subscription['status'] === 'active'): ?>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#generateKeyModal">
                        <i class="fas fa-plus me-1"></i>
                        Nova Chave
                    </button>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (!empty($apiKeys)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Nome</th>
                                    <th>Status</th>
                                    <th>Uso Mensal</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($apiKeys as $key): ?>
                                    <tr>
                                        <td><?= e($key['name']) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $key['status'] === 'active' ? 'success' : 'danger' ?> fs-6">
                                                <?= ucfirst($key['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?= number_format($key['monthly_usage']) ?>
                                            <?php if ($key['monthly_limit']): ?>
                                                / <?= number_format($key['monthly_limit']) ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                                    onclick="showApiKey('<?= e($key['api_key']) ?>')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if ($key['status'] === 'active'): ?>
                                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                                        onclick="revokeApiKey(<?= $key['id'] ?>)">
                                                    <i class="fas fa-ban"></i>
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-key fa-3x text-muted mb-3"></i>
                        <h5>Nenhuma chave de API</h5>
                        <p class="text-muted">Gere sua primeira chave de API para começar a usar o serviço.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal para gerar nova chave -->
<div class="modal fade" id="generateKeyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Gerar Nova Chave de API</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="generateKeyForm">
                <div class="modal-body">
                    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                    <div class="mb-3">
                        <label for="keyName" class="form-label">Nome da Chave</label>
                        <input type="text" class="form-control" id="keyName" name="name" 
                               placeholder="Ex: Chave do Site Principal" required>
                        <div class="form-text">Escolha um nome descritivo para identificar esta chave.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Gerar Chave</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para exibir chave -->
<div class="modal fade" id="showKeyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chave de API</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Importante:</strong> Mantenha sua chave de API segura e não a compartilhe.
                </div>
                <div class="mb-3">
                    <label class="form-label">Sua Chave de API:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="apiKeyValue" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyApiKey()">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>

<script>
// Gerar nova chave de API
document.getElementById('generateKeyForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('<?= url('/client/generate-api-key') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Fechar modal de geração
            bootstrap.Modal.getInstance(document.getElementById('generateKeyModal')).hide();
            
            // Mostrar chave gerada
            document.getElementById('apiKeyValue').value = data.api_key;
            new bootstrap.Modal(document.getElementById('showKeyModal')).show();
            
            // Recarregar página após 3 segundos
            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao gerar chave de API');
    });
});

// Mostrar chave existente
function showApiKey(apiKey) {
    document.getElementById('apiKeyValue').value = apiKey;
    new bootstrap.Modal(document.getElementById('showKeyModal')).show();
}

// Copiar chave para clipboard
function copyApiKey() {
    const input = document.getElementById('apiKeyValue');
    input.select();
    document.execCommand('copy');
    
    // Feedback visual
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    setTimeout(() => {
        button.innerHTML = originalHTML;
    }, 2000);
}

// Revogar chave de API
function revokeApiKey(keyId) {
    if (confirm('Tem certeza que deseja revogar esta chave? Esta ação não pode ser desfeita.')) {
        const formData = new FormData();
        formData.append('_token', '<?= $csrf_token ?>');
        
        fetch(`<?= url('/client/revoke-api-key/') ?>${keyId}`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao revogar chave de API');
        });
    }
}
</script>
