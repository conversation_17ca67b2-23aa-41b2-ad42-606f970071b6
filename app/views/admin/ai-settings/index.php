<?php
$title = 'Configurações de Geração de Conteúdo - E1Copy AI';
$pageTitle = 'Configurações de Geração de Conteúdo';
ob_start();
?>

<style>
.settings-section {
    border-left: 4px solid #007bff;
    margin-bottom: 2rem;
}
.variable-tag {
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: monospace;
    font-size: 0.875rem;
    margin: 0.125rem;
    display: inline-block;
}
.template-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: monospace;
    font-size: 0.875rem;
    max-height: 200px;
    overflow-y: auto;
}
</style>

<!-- Estatísticas de Uso -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary"><?= number_format($usageStats['total_generated']) ?></h3>
                <p class="text-muted mb-0">Posts Gerados</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success"><?= number_format($usageStats['successful']) ?></h3>
                <p class="text-muted mb-0">Sucessos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-danger"><?= number_format($usageStats['failed']) ?></h3>
                <p class="text-muted mb-0">Falhas</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info"><?= number_format($usageStats['last_30_days']) ?></h3>
                <p class="text-muted mb-0">Últimos 30 dias</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Configurações -->
    <div class="col-md-8">
        <form method="POST" action="<?= url('/admin/ai-settings') ?>">
            <input type="hidden" name="_token" value="<?= $csrf_token ?>">
            
            <?php foreach ($settingsGrouped as $category => $settings): ?>
                <div class="card settings-section">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-<?= $this->getCategoryIcon($category) ?> me-2"></i>
                            <?= e($category) ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($settings as $setting): ?>
                                <div class="col-md-6 mb-3">
                                    <label for="setting_<?= $setting['setting_key'] ?>" class="form-label">
                                        <?= e($this->getSettingLabel($setting['setting_key'])) ?>
                                    </label>
                                    
                                    <?php if ($setting['setting_type'] === 'boolean'): ?>
                                        <select class="form-select" name="settings[<?= $setting['setting_key'] ?>]" id="setting_<?= $setting['setting_key'] ?>">
                                            <option value="true" <?= $setting['setting_value'] === 'true' ? 'selected' : '' ?>>Sim</option>
                                            <option value="false" <?= $setting['setting_value'] === 'false' ? 'selected' : '' ?>>Não</option>
                                        </select>
                                    <?php elseif ($setting['setting_type'] === 'json'): ?>
                                        <textarea class="form-control" name="settings[<?= $setting['setting_key'] ?>]" 
                                                  id="setting_<?= $setting['setting_key'] ?>" rows="3"
                                                  placeholder="JSON válido"><?= e($setting['setting_value']) ?></textarea>
                                    <?php elseif ($setting['setting_key'] === 'groq_api_key'): ?>
                                        <input type="password" class="form-control" name="settings[<?= $setting['setting_key'] ?>]" 
                                               id="setting_<?= $setting['setting_key'] ?>" value="<?= e($setting['setting_value']) ?>"
                                               placeholder="Sua chave da API do Groq">
                                    <?php else: ?>
                                        <input type="<?= $setting['setting_type'] === 'number' ? 'number' : 'text' ?>" 
                                               class="form-control" name="settings[<?= $setting['setting_key'] ?>]" 
                                               id="setting_<?= $setting['setting_key'] ?>" value="<?= e($setting['setting_value']) ?>"
                                               <?= $setting['setting_type'] === 'number' ? 'min="0"' : '' ?>>
                                    <?php endif; ?>
                                    
                                    <?php if ($setting['description']): ?>
                                        <div class="form-text"><?= e($setting['description']) ?></div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    Salvar Configurações
                </button>
            </div>
        </form>
    </div>
    
    <!-- Templates -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-code me-2"></i>
                    Templates
                </h5>
                <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#newTemplateModal">
                    <i class="fas fa-plus me-1"></i>
                    Novo
                </button>
            </div>
            <div class="card-body">
                <?php if (empty($templates)): ?>
                    <p class="text-muted text-center">Nenhum template encontrado</p>
                <?php else: ?>
                    <?php foreach ($templates as $template): ?>
                        <div class="border rounded p-3 mb-3 <?= $template['is_default'] ? 'border-primary' : '' ?>">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">
                                    <?= e($template['name']) ?>
                                    <?php if ($template['is_default']): ?>
                                        <span class="badge bg-primary ms-1">Padrão</span>
                                    <?php endif; ?>
                                </h6>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <?php if (!$template['is_default']): ?>
                                            <li><a class="dropdown-item" href="#" onclick="setDefaultTemplate(<?= $template['id'] ?>)">Definir como Padrão</a></li>
                                        <?php endif; ?>
                                        <li><a class="dropdown-item" href="#" onclick="toggleTemplateStatus(<?= $template['id'] ?>)">
                                            <?= $template['status'] === 'active' ? 'Desativar' : 'Ativar' ?>
                                        </a></li>
                                        <?php if (!$template['is_default']): ?>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteTemplate(<?= $template['id'] ?>)">Excluir</a></li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </div>
                            
                            <?php if ($template['description']): ?>
                                <p class="text-muted small mb-2"><?= e($template['description']) ?></p>
                            <?php endif; ?>
                            
                            <div class="mb-2">
                                <small class="text-muted">Variáveis:</small><br>
                                <?php 
                                $variables = json_decode($template['variables'], true) ?: [];
                                foreach ($variables as $var): 
                                ?>
                                    <span class="variable-tag">{{<?= e($var) ?>}}</span>
                                <?php endforeach; ?>
                            </div>
                            
                            <div class="template-preview">
                                <?= e(substr($template['html_template'], 0, 200)) ?>
                                <?php if (strlen($template['html_template']) > 200): ?>
                                    <span class="text-muted">...</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal Novo Template -->
<div class="modal fade" id="newTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Novo Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="newTemplateForm">
                <div class="modal-body">
                    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                    
                    <div class="mb-3">
                        <label for="template_name" class="form-label">Nome do Template</label>
                        <input type="text" class="form-control" id="template_name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="template_description" class="form-label">Descrição</label>
                        <textarea class="form-control" id="template_description" name="description" rows="2"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="template_html" class="form-label">Template HTML</label>
                        <textarea class="form-control" id="template_html" name="html_template" rows="8" required
                                  placeholder="<h1>{{titulo}}</h1>&#10;<div>{{conteudo}}</div>"></textarea>
                        <div class="form-text">Use variáveis no formato {{nome_da_variavel}}</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <label for="template_variables" class="form-label">Variáveis Disponíveis</label>
                            <textarea class="form-control" id="template_variables" name="variables" rows="4" required
                                      placeholder='["titulo", "conteudo", "categoria"]'></textarea>
                            <div class="form-text">Array JSON com as variáveis</div>
                        </div>
                        <div class="col-md-6">
                            <label for="template_required" class="form-label">Variáveis Obrigatórias</label>
                            <textarea class="form-control" id="template_required" name="required_variables" rows="4" required
                                      placeholder='["titulo", "conteudo"]'></textarea>
                            <div class="form-text">Array JSON com variáveis obrigatórias</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Criar Template</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Funções para gerenciar templates
function setDefaultTemplate(id) {
    if (confirm('Definir este template como padrão?')) {
        fetch(`<?= url('/admin/ai-settings/templates') ?>/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                _token: '<?= $csrf_token ?>',
                action: 'set_default'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        });
    }
}

function toggleTemplateStatus(id) {
    fetch(`<?= url('/admin/ai-settings/templates') ?>/${id}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            _token: '<?= $csrf_token ?>',
            action: 'toggle_status'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    });
}

function deleteTemplate(id) {
    if (confirm('Tem certeza que deseja excluir este template?')) {
        fetch(`<?= url('/admin/ai-settings/templates') ?>/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                _token: '<?= $csrf_token ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        });
    }
}

// Formulário de novo template
document.getElementById('newTemplateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('<?= url('/admin/ai-settings/templates') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    });
});
</script>

<?php
// Funções auxiliares para a view
function getCategoryIcon($category) {
    switch ($category) {
        case 'Groq API': return 'robot';
        case 'Padrões': return 'cog';
        case 'Limites': return 'tachometer-alt';
        case 'Fila': return 'list';
        default: return 'sliders-h';
    }
}

function getSettingLabel($key) {
    $labels = [
        'groq_api_key' => 'Chave da API',
        'groq_model' => 'Modelo',
        'default_template_id' => 'Template Padrão',
        'max_tokens' => 'Máximo de Tokens',
        'temperature' => 'Temperatura',
        'default_variables' => 'Variáveis Padrão',
        'required_variables' => 'Variáveis Obrigatórias',
        'post_generation_enabled' => 'Geração Habilitada',
        'queue_processing_enabled' => 'Processamento da Fila',
        'max_posts_per_day' => 'Posts por Dia'
    ];
    
    return $labels[$key] ?? ucfirst(str_replace('_', ' ', $key));
}

$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
