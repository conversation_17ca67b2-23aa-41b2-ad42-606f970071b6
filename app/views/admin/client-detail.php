<?php
$title = 'Cliente: ' . e($client['name']) . ' - E1Copy AI';
$pageTitle = 'Detalhes do Cliente';
ob_start();
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= url('/admin/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= url('/admin/clients') ?>">Clientes</a></li>
        <li class="breadcrumb-item active"><?= e($client['name']) ?></li>
    </ol>
</nav>

<div class="row">
    <!-- Informações do Cliente -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    Informações do Cliente
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-user fa-2x text-white"></i>
                    </div>
                </div>
                
                <div class="text-center mb-3">
                    <h5 class="mb-1"><?= e($client['name']) ?></h5>
                    <p class="text-muted mb-2"><?= e($client['email']) ?></p>
                    <span class="badge bg-<?= $client['status'] === 'active' ? 'success' : ($client['status'] === 'suspended' ? 'warning' : 'danger') ?> fs-6">
                        <?= ucfirst($client['status']) ?>
                    </span>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <strong>Cadastro</strong><br>
                        <small><?= date('d/m/Y', strtotime($client['created_at'])) ?></small>
                    </div>
                    <div class="col-6">
                        <strong>Última Atualização</strong><br>
                        <small><?= date('d/m/Y', strtotime($client['updated_at'])) ?></small>
                    </div>
                </div>
                
                <hr>
                
                <!-- Ações Rápidas -->
                <div class="d-grid gap-2">
                    <form method="POST" action="<?= url("/admin/clients/{$client['id']}") ?>" class="d-inline">
                        <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                        <input type="hidden" name="action" value="update_status">
                        <input type="hidden" name="status" value="<?= $client['status'] === 'active' ? 'suspended' : 'active' ?>">
                        <button type="submit" class="btn btn-<?= $client['status'] === 'active' ? 'warning' : 'success' ?> w-100">
                            <i class="fas fa-<?= $client['status'] === 'active' ? 'pause' : 'play' ?> me-1"></i>
                            <?= $client['status'] === 'active' ? 'Suspender' : 'Ativar' ?> Cliente
                        </button>
                    </form>
                    
                    <div class="btn-group w-100" role="group">
                        <form method="POST" action="<?= url("/admin/clients/{$client['id']}") ?>" class="flex-fill">
                            <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                            <input type="hidden" name="action" value="suspend_keys">
                            <button type="submit" class="btn btn-outline-warning w-100">
                                <i class="fas fa-ban me-1"></i>
                                Suspender Chaves
                            </button>
                        </form>
                        <form method="POST" action="<?= url("/admin/clients/{$client['id']}") ?>" class="flex-fill">
                            <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                            <input type="hidden" name="action" value="activate_keys">
                            <button type="submit" class="btn btn-outline-success w-100">
                                <i class="fas fa-check me-1"></i>
                                Ativar Chaves
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Estatísticas de Uso -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Estatísticas de Uso
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-primary"><?= number_format($usageStats['total_requests']) ?></h4>
                            <small class="text-muted">Total de Requisições</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-success"><?= number_format($usageStats['today_requests']) ?></h4>
                            <small class="text-muted">Hoje</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-info"><?= number_format($usageStats['month_requests']) ?></h4>
                            <small class="text-muted">Este Mês</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-warning"><?= count($apiKeys) ?></h4>
                            <small class="text-muted">Chaves de API</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Assinaturas -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    Assinaturas
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($subscriptions)): ?>
                    <?php foreach ($subscriptions as $subscription): ?>
                        <div class="border rounded p-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1"><?= e($subscription['plan_name']) ?></h6>
                                    <small class="text-muted">
                                        Criada em <?= date('d/m/Y', strtotime($subscription['created_at'])) ?>
                                    </small>
                                </div>
                                <span class="badge bg-<?= $subscription['status'] === 'active' ? 'success' : 'secondary' ?>">
                                    <?= ucfirst($subscription['status']) ?>
                                </span>
                            </div>
                            
                            <?php if ($subscription['status'] === 'active'): ?>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php if ($subscription['ends_at']): ?>
                                            Expira em <?= date('d/m/Y', strtotime($subscription['ends_at'])) ?>
                                        <?php else: ?>
                                            Assinatura vitalícia
                                        <?php endif; ?>
                                    </small>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h6>Nenhuma assinatura</h6>
                        <p class="text-muted">Este cliente ainda não possui assinaturas.</p>
                        <button type="button" class="btn btn-primary" disabled>
                            <i class="fas fa-plus me-1"></i>
                            Criar Assinatura (Em breve)
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Chaves de API -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-key me-2"></i>
                    Chaves de API
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($apiKeys)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Nome</th>
                                    <th>Status</th>
                                    <th>Uso Mensal</th>
                                    <th>Último Uso</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($apiKeys as $key): ?>
                                    <tr>
                                        <td><?= e($key['name']) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $key['status'] === 'active' ? 'success' : 'danger' ?> fs-6">
                                                <?= ucfirst($key['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?= number_format($key['monthly_usage']) ?>
                                            <?php if ($key['monthly_limit']): ?>
                                                / <?= number_format($key['monthly_limit']) ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($key['last_used_at']): ?>
                                                <?= date('d/m/Y H:i', strtotime($key['last_used_at'])) ?>
                                            <?php else: ?>
                                                <span class="text-muted">Nunca</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-key fa-3x text-muted mb-3"></i>
                        <h6>Nenhuma chave de API</h6>
                        <p class="text-muted">Este cliente ainda não possui chaves de API.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Atividade Recente -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-history me-2"></i>
            Atividade Recente
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Em desenvolvimento:</strong> Em breve você poderá visualizar a atividade detalhada deste cliente.
        </div>
        
        <div class="text-center py-4">
            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
            <h6>Logs de Atividade</h6>
            <p class="text-muted">Histórico de requisições, logins e ações do cliente aparecerão aqui.</p>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>

<script>
// Confirmação para ações críticas
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function(e) {
        const action = this.querySelector('input[name="action"]')?.value;
        
        if (action === 'update_status') {
            const status = this.querySelector('input[name="status"]').value;
            const actionText = status === 'active' ? 'ativar' : 'suspender';
            
            if (!confirm(`Tem certeza que deseja ${actionText} este cliente?`)) {
                e.preventDefault();
            }
        } else if (action === 'suspend_keys') {
            if (!confirm('Tem certeza que deseja suspender todas as chaves de API deste cliente?')) {
                e.preventDefault();
            }
        } else if (action === 'activate_keys') {
            if (!confirm('Tem certeza que deseja ativar todas as chaves de API deste cliente?')) {
                e.preventDefault();
            }
        }
    });
});
</script>
