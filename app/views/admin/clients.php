<?php
$title = 'Gerenciar Clientes - E1Copy AI';
$pageTitle = 'Gerenciamento de Clientes';
ob_start();
?>

<!-- Filtros -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Buscar</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= e($search) ?>" placeholder="Nome ou email...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">Todos</option>
                    <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Ativo</option>
                    <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>Inativo</option>
                    <option value="suspended" <?= $status === 'suspended' ? 'selected' : '' ?>>Suspenso</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>
                    Filtrar
                </button>
                <a href="<?= url('/admin/clients') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    Limpar
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Lista de Clientes -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>
            Clientes (<?= number_format($clients['total']) ?>)
        </h5>
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addClientModal">
            <i class="fas fa-plus me-1"></i>
            Novo Cliente
        </button>
    </div>
    <div class="card-body">
        <?php if (!empty($clients['data'])): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Cliente</th>
                            <th>Plano</th>
                            <th>Status</th>
                            <th>Cadastro</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($clients['data'] as $client): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?= e($client['name']) ?></strong><br>
                                        <small class="text-muted"><?= e($client['email']) ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($client['subscription']): ?>
                                        <span class="badge bg-primary">
                                            <?= e($client['subscription']['plan_name']) ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Sem plano</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $client['status'] === 'active' ? 'success' : ($client['status'] === 'suspended' ? 'warning' : 'danger') ?>">
                                        <?= ucfirst($client['status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?= date('d/m/Y', strtotime($client['created_at'])) ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= url("/admin/clients/{$client['id']}") ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-warning btn-sm" 
                                                onclick="toggleClientStatus(<?= $client['id'] ?>, '<?= $client['status'] ?>')">
                                            <i class="fas fa-<?= $client['status'] === 'active' ? 'pause' : 'play' ?>"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteClient(<?= $client['id'] ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Paginação -->
            <?php if ($clients['total_pages'] > 1): ?>
                <nav aria-label="Paginação">
                    <ul class="pagination justify-content-center">
                        <?php if ($clients['has_prev']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $clients['current_page'] - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>">
                                    Anterior
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = 1; $i <= $clients['total_pages']; $i++): ?>
                            <li class="page-item <?= $i === $clients['current_page'] ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($clients['has_next']): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $clients['current_page'] + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>">
                                    Próximo
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5>Nenhum cliente encontrado</h5>
                <p class="text-muted">
                    <?= $search || $status ? 'Tente ajustar os filtros de busca.' : 'Adicione o primeiro cliente para começar.' ?>
                </p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal para adicionar cliente -->
<div class="modal fade" id="addClientModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Adicionar Novo Cliente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addClientForm">
                <div class="modal-body">
                    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                    
                    <div class="mb-3">
                        <label for="clientName" class="form-label">Nome Completo</label>
                        <input type="text" class="form-control" id="clientName" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="clientEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="clientEmail" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="clientPassword" class="form-label">Senha</label>
                        <input type="password" class="form-control" id="clientPassword" name="password" 
                               placeholder="Mínimo 6 caracteres" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="clientPlan" class="form-label">Plano</label>
                        <select class="form-select" id="clientPlan" name="plan_id">
                            <option value="">Selecionar depois</option>
                            <!-- Planos serão carregados via JavaScript -->
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-success">Criar Cliente</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>

<script>
// Alternar status do cliente
function toggleClientStatus(clientId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'suspended' : 'active';
    const action = newStatus === 'active' ? 'ativar' : 'suspender';
    
    if (confirm(`Tem certeza que deseja ${action} este cliente?`)) {
        const formData = new FormData();
        formData.append('_token', '<?= $csrf_token ?>');
        formData.append('action', 'update_status');
        formData.append('status', newStatus);
        
        fetch(`<?= url('/admin/clients/') ?>${clientId}`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            location.reload();
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao alterar status do cliente');
        });
    }
}

// Excluir cliente
function deleteClient(clientId) {
    if (confirm('Tem certeza que deseja excluir este cliente? Esta ação não pode ser desfeita.')) {
        // Implementar exclusão se necessário
        alert('Funcionalidade de exclusão não implementada por segurança.');
    }
}

// Carregar planos no modal
document.addEventListener('DOMContentLoaded', function() {
    // Simular carregamento de planos
    const planSelect = document.getElementById('clientPlan');
    const plans = [
        {id: 1, name: 'Básico - R$ 29,90/mês'},
        {id: 2, name: 'Profissional - R$ 79,90/mês'},
        {id: 3, name: 'Empresarial - R$ 199,90/mês'}
    ];
    
    plans.forEach(plan => {
        const option = document.createElement('option');
        option.value = plan.id;
        option.textContent = plan.name;
        planSelect.appendChild(option);
    });
});

// Adicionar cliente
document.getElementById('addClientForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('Funcionalidade de adicionar cliente será implementada em breve.');
});
</script>
